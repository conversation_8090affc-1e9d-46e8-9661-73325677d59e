# SynapseCV - AI驱动的简历优化平台

## 项目概述

**SynapseCV: One Master Profile. Infinite Targeted Resumes.**

SynapseCV 是一个专注于解决 SDE/技术岗位求职者在美国市场投递简历时，需要频繁手动定制化简历痛点的平台。通过基于结构化数据和 AI 智能优化，实现简历的动态版本管理。

**核心价值主张：** 通过"一改全改"机制，让求职者从单一数据源生成多个定制化简历版本，大幅提升求职效率。

**项目状态：** 当前项目已完成MVP阶段部署，包括完整的用户认证系统、核心业务逻辑和数据库集成，前端应用正常运行。

## 项目目标

- **业务目标：** 2 个月内急速开发，达到每月 $1000 净收入
- **用户目标：** 达到 53 个付费订阅用户（按 $19/月计算）
- **关键结果：** 试用用户到付费用户的转化率 ≥8%

## 技术架构

### 技术栈
- **前端：** Next.js 15 + React + TypeScript + Tailwind CSS v4 + PWA ✅
- **数据库：** Supabase (PostgreSQL) + 行级安全策略(RLS) ✅
- **认证：** Supabase Auth + JWT ✅
- **状态管理：** Zustand + 持久化存储 ✅
- **AI 服务：** OpenAI GPT-4 (主) + Perplexity (辅) 🔄
- **支付：** Stripe 🔄
- **自动化：** n8n 🔄
- **部署：** 本地开发环境 ✅ / 生产部署 🔄

### 核心功能
1. **结构化数据录入** - 录入原始事实 (Raw Facts)
2. **AI 子弹点优化** - 基于目标岗位的智能优化 (SDE 垂直领域深度优化)
3. **动态版本管理** - "一改全改"机制，无版本数量限制
4. **ATS 友好模板** - 专业简历模板
5. **永久试用机制** - 免费试用，带水印限制
6. **Stripe 付费集成** - 订阅模式支付
7. **邮件自动化** - n8n 驱动的欢迎/转化邮件

## 文档结构

### 需求文档
- [产品需求文档（PRD）](./docs/需求文档/产品需求文档（PRD）框架.md) - 产品功能需求和用户故事
- [软件需求规格说明书（SRS）](./docs/需求文档/软件需求规格说明书（SRS）框架.md) - 技术实现需求
- [原型与用户流程规划](./docs/需求文档/原型与用户流程规划框架.md) - 用户界面和交互设计

### 部署与开发文档
- [API 契约与后端实现](./docs/部署与开发文档/API%20契约与后端实现文档.md) - API 接口规范
- [架构与规范文档](./docs/部署与开发文档/架构与规范文档.md) - 技术架构和开发规范
- [DevOps 部署与运维](./docs/部署与开发文档/DevOps%20部署与运维文档.md) - 部署和运维指南

### 运维文档
- [故障与安全响应手册](./docs/运维文档/故障与安全响应手册%20(SOP).md) - 故障处理和安全响应流程
- [技术债务管理框架](./docs/运维文档/持续改进与技术债务管理框架.md) - 技术债务和改进计划
- [数据保护与合规政策](./docs/运维文档/数据保护与合规政策.md) - 数据保留政策和GDPR合规
- [可扩展性与健康报告](./docs/运维文档/可扩展性、健康检查与成本控制报告.md) - 系统健康监控

## 快速开始

### 本地开发环境部署

#### 前置要求
- Node.js 18+
- npm 或 yarn
- Supabase云端项目

#### 1. 克隆项目

```bash
git clone https://github.com/your-username/SynapseCV.git
cd SynapseCV
```

#### 2. 配置Supabase云端项目

确保您的Supabase云端项目已正确配置：
- 数据库表结构已创建
- RLS (行级安全策略) 已启用
- 认证设置已配置

#### 3. 配置前端环境

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量 (使用云端Supabase配置)
# NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

#### 4. 启动前端应用

```bash
# 启动开发服务器
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

#### 5. 验证部署

应用启动后，您可以：
- 访问注册页面创建新用户账户
- 登录后管理个人资料和工作经历
- 创建和管理简历版本

**注意：** 云端Supabase需要邮件确认，注册后请检查邮箱并点击确认链接。

#### 6. 认证系统测试

为了验证认证系统是否正常工作，您可以访问以下测试页面：

- **手动测试页面**: [http://localhost:3000/auth-test](http://localhost:3000/auth-test)
  - 提供完整的认证功能测试界面
  - 可以测试注册、登录、登出、session检查等功能
  - 显示详细的测试结果和用户状态信息

- **集成测试页面**: [http://localhost:3000/integration-test](http://localhost:3000/integration-test)
  - 自动化测试认证系统的完整流程
  - 一键运行所有认证相关测试
  - 提供测试结果报告

**认证系统特性：**
- ✅ 自动session恢复：页面刷新后保持登录状态
- ✅ 路由保护：未登录用户自动重定向到登录页面
- ✅ 状态同步：前端状态与Supabase session实时同步
- ✅ 错误处理：完善的错误处理和用户反馈机制

### 生产环境部署

#### Supabase云端部署 ✅
- 已配置云端Supabase项目
- 数据库表结构已创建
- RLS策略已配置
- 认证系统已启用

#### 前端部署 (Vercel推荐)
1. 连接GitHub仓库到Vercel
2. 配置环境变量 (使用云端Supabase配置)
3. 自动部署

详细部署指南请参考 [DevOps 部署与运维文档](./docs/部署与开发文档/DevOps%20部署与运维文档.md)。

### 环境变量配置（规划中）
```env
# 数据库
DATABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# AI 服务
OPENAI_API_KEY=your_openai_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key

# 支付
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# 认证
JWT_SECRET=your_jwt_secret

# 邮件自动化
N8N_WEBHOOK_URL=your_n8n_webhook_url
```

## 项目状态

### 当前阶段：MVP核心功能部署完成 ✅

#### 文档规划阶段 ✅
- ✅ 产品需求文档（PRD）框架
- ✅ 软件需求规格说明书（SRS）框架
- ✅ 原型与用户流程规划框架
- ✅ 架构与规范文档
- ✅ API 契约与后端实现文档
- ✅ 数据库设计与契约
- ✅ DevOps 部署与运维文档
- ✅ 运维文档（故障响应、技术债务、数据保护等）

#### 前端开发阶段 ✅
- ✅ Next.js 15前端项目初始化
- ✅ TypeScript + Tailwind CSS v4配置
- ✅ 基础UI组件库（Button、Input、Loading等）
- ✅ 页面布局组件（Header、Footer、MainLayout）
- ✅ Zustand状态管理和持久化
- ✅ Supabase客户端集成

#### 数据库部署阶段 ✅
- ✅ Supabase数据库初始化SQL脚本
- ✅ 核心表结构（用户、经历、版本、日志）
- ✅ 行级安全策略(RLS)配置
- ✅ 性能优化索引和JSONB支持
- ✅ 数据验证约束和触发器
- ✅ 本地开发环境部署

#### 认证系统阶段 ✅
- ✅ 用户注册和登录功能
- ✅ 密码重置流程
- ✅ 认证状态管理和持久化
- ✅ 路由保护和权限控制
- ✅ RLS策略验证和优化

#### 核心业务逻辑阶段 ✅
- ✅ 用户资料管理（CRUD）
- ✅ 工作经历管理（Raw Facts录入）
- ✅ 简历版本管理（配置和模板）
- ✅ 数据类型转换和验证
- ✅ "一改全改"机制基础架构

#### 前端页面开发阶段 ✅
- ✅ 首页和导航
- ✅ 用户注册/登录页面
- ✅ 用户控制台/仪表板
- ✅ 个人资料管理页面
- ✅ 经历列表和添加页面
- ✅ 简历版本管理页面
- ✅ 错误处理和加载状态

#### 集成测试阶段 ✅
- ✅ 数据库连接和CRUD测试
- ✅ 用户认证流程测试
- ✅ RLS权限控制验证
- ✅ MVP功能端到端测试
- ✅ 完整用户工作流程验证

### MVP 核心功能状态
- ✅ 用户注册和认证系统
- ✅ 用户资料管理
- ✅ 工作经历录入（Raw Facts）
- ✅ 简历版本创建和管理
- ✅ 数据安全和权限控制
- ✅ 响应式前端界面
- 🔄 AI 子弹点优化（待集成OpenAI）
- 🔄 PDF 导出功能
- 🔄 Stripe 支付集成
- 🔄 邮件自动化（n8n集成）
- 🔄 永久试用机制（带水印限制）

### 未来规划 (Phase 2)
- 📋 n8n 集成投递追踪
- 📋 内置 ATS 评分器
- 📋 社交/社区功能
- 📋 DOCX 导出
- 📋 多语言支持
- 📋 移动端应用

## 贡献指南

### 当前阶段说明
项目目前处于文档规划阶段，代码实现尚未开始。如果您希望参与项目开发，请先阅读相关文档：

1. **架构与规范文档** - 了解技术架构和开发规范
2. **API 契约与后端实现文档** - 了解API设计规范
3. **数据库设计与契约** - 了解数据库结构设计

### 开发流程（代码实现阶段）
1. 创建功能分支：`git checkout -b feature/your-feature`
2. 提交更改：`git commit -m 'Add some feature'`
3. 推送分支：`git push origin feature/your-feature`
4. 创建 Pull Request

### 代码规范
- 遵循项目编码规范（详见架构文档）
- 所有代码必须通过 ESLint 检查
- 新功能必须包含测试用例
- 提交前运行完整的测试套件

## 监控和告警（规划中）

### 关键指标（系统上线后监控）
- **OpenAI GPT-4 API 错误率：** ≤ 1%
- **OpenAI GPT-4 调用延迟 (P95)：** ≤ 5 秒
- **Perplexity API 错误率：** ≤ 2%
- **Perplexity 调用延迟 (P95)：** ≤ 3 秒
- **API 错误率：** < 1%
- **API 延迟 (P95)：** < 300ms
- **系统可用性：** 99.9%

### 告警通知（规划中）
- P0 事件：立即通知 (Slack + 邮件 + 短信)
- P1 事件：15分钟内通知 (Slack + 邮件)
- P2 事件：1小时内通知 (邮件)

**注意：** 详细的监控和告警配置将在系统部署时实现，相关配置文档请参考 [可扩展性、健康检查与成本控制报告](./docs/运维文档/可扩展性、健康检查与成本控制报告.md)。

## 安全与合规（规划中）

### 数据保护策略
- 简历内容使用 AES-256 加密存储
- 所有通信使用 HTTPS
- 实施 Row Level Security (RLS)
- 定期安全审计和漏洞扫描
- 遵循 GDPR 和 CCPA 合规要求
- 数据保留政策：订阅结束 + 90天宽限期

### 隐私政策（规划中）
- 用户数据仅用于提供服务
- 不向第三方分享用户数据
- 用户可随时删除账户和数据

**注意：** 详细的安全与合规策略将在系统实现阶段具体实施，相关文档请参考 [数据保护与合规政策](./docs/运维文档/数据保护与合规政策.md)。

## 支持与联系

### 技术支持
- 邮箱：<EMAIL>
- 文档：https://docs.synapsecv.com
- 状态页面：https://status.synapsecv.com

### 商业合作
- 邮箱：<EMAIL>
- 电话：+1-xxx-xxx-xxxx

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 更新日志

### v0.5.1 (2025-01-08) - 认证系统修复完成 ✅
- 🔧 修复Supabase认证配置，添加PKCE流程提高安全性
- 🔧 优化认证状态管理，改进Zustand store的session同步逻辑
- 🔧 完善AuthProvider组件，添加更可靠的初始化和状态监听机制
- 🔧 修复路由保护机制，优化AuthGuard和GuestGuard的重定向逻辑
- 🔧 改进session持久化和恢复机制，确保页面刷新后状态正确
- 🔧 添加认证状态的自动检查和同步功能
- 🔧 优化错误处理，提供更好的用户体验
- ✅ 创建认证测试页面(/auth-test)用于手动测试
- ✅ 创建集成测试页面(/integration-test)用于自动化测试
- ✅ 验证完整的认证流程：注册、登录、登出、session恢复
- 📝 更新开发文档，记录认证系统修复过程

### v0.5.0 (2025-01-07) - MVP核心功能部署完成 ✅
- 🚀 完成MVP阶段核心业务逻辑实现
- 🚀 实现用户资料管理（创建、读取、更新）
- 🚀 实现工作经历管理（Raw Facts结构化录入）
- 🚀 实现简历版本管理（配置化版本创建）
- 🚀 创建完整的前端页面组件（经历、版本、资料）
- 🚀 建立"一改全改"机制的基础架构
- 🔧 修复数据库schema和前端类型不匹配问题
- 🔧 优化JSONB字段的类型转换处理
- 🔧 完善表单验证和错误处理机制
- 🔧 实现响应式UI设计和用户体验优化
- ✅ 完成数据库集成测试（100%通过）
- ✅ 完成认证流程测试（100%通过）
- ✅ 完成MVP功能集成测试（100%通过）
- 📝 更新README和部署文档
- 📝 创建完整的本地开发环境指南

### v0.4.0 (2024-12-19) - 认证系统修复完成
- 🔧 修复Supabase认证系统的RLS策略问题
- 🔧 优化用户注册和登录流程，增强错误处理
- 🔧 创建认证保护组件和路由守卫机制
- 🔧 实现用户控制台页面和认证状态管理
- 🔧 添加认证提供者组件，监听认证状态变化
- 🔧 创建多个诊断和修复工具页面
- 🔧 完善前端认证流程，支持自动重定向
- 🔧 修复数据库用户资料创建问题
- 🔧 添加集成测试页面验证修复效果
- 📝 更新开发文档，记录认证系统修复过程

### v0.3.0 (2024-12-19) - 数据库初始化完成
- 🗄️ 基于数据库设计文档创建完整的Supabase数据库初始化SQL
- 🗄️ 实现核心表结构：user_profiles, master_experiences, optimized_content, resume_versions, n8n_automation_log
- 🗄️ 配置行级安全策略(RLS)确保数据安全隔离
- 🗄️ 创建性能优化索引，特别是JSONB字段的GIN索引
- 🗄️ 添加数据验证约束和更新时间触发器
- 🗄️ 提供Supabase专用迁移脚本和完整初始化脚本
- 📝 创建详细的数据库部署指南和README文档
- 📝 更新项目README，记录数据库初始化完成

### v0.2.1 (2024-12-19) - Tailwind CSS v4 配置修复
- 🔧 修复Tailwind CSS v4配置问题，更新为正确的v4语法
- 🔧 更新PostCSS配置以兼容Tailwind CSS v4
- 🔧 修复globals.css文件，使用新的@import语法
- 🔧 移除tailwind.config.ts中的content配置（v4中已移至CSS文件）
- ✅ 验证样式渲染正常，构建成功
- 📝 更新开发文档，记录Tailwind CSS v4配置变更

### v0.2.0 (2024-12-19) - 前端初始化阶段
- 🚀 完成Next.js 15项目初始化
- 🚀 配置TypeScript、Tailwind CSS v4和PWA支持
- 🚀 创建基础UI组件库（Button、Input、Loading等）
- 🚀 搭建页面布局组件（Header、Footer、MainLayout）
- 🚀 实现Zustand状态管理（认证、简历数据）
- 🚀 配置Supabase客户端和类型定义
- 🚀 创建登录和注册页面
- 🚀 设置开发环境和构建配置
- 🚀 编写开发文档和README

### v0.1.0 (2024-12-19) - 文档规划阶段
- 📋 完成产品需求文档（PRD）框架
- 📋 完成软件需求规格说明书（SRS）框架
- 📋 完成原型与用户流程规划框架
- 📋 完成架构与规范文档
- 📋 完成API 契约与后端实现文档
- 📋 完成数据库设计与契约
- 📋 完成DevOps 部署与运维文档
- 📋 完成运维文档（故障响应、技术债务、数据保护等）

### v1.0.0 (计划中) - MVP 版本
- 🔄 用户注册和认证功能
- 🔄 核心业务逻辑实现
- 🔄 基础部署和监控配置

---

**注意：** 本文档会随着项目发展持续更新，请定期查看最新版本。
