# SynapseCV 部署指南

## 概述

本文档提供SynapseCV项目的完整部署指南，包括本地开发环境和生产环境的部署步骤。

## 系统要求

### 本地开发环境
- **Node.js:** 18.0.0 或更高版本
- **npm:** 9.0.0 或更高版本
- **Git:** 2.30.0 或更高版本
- **操作系统:** Windows 10+, macOS 10.15+, Ubuntu 20.04+

### 生产环境
- **Supabase:** 云端PostgreSQL数据库
- **Vercel/Netlify:** 前端静态部署
- **域名:** 自定义域名（可选）

## 本地开发环境部署

### 1. 项目克隆和初始化

```bash
# 克隆项目
git clone https://github.com/your-username/SynapseCV.git
cd SynapseCV

# 检查Node.js版本
node --version  # 应该 >= 18.0.0
npm --version   # 应该 >= 9.0.0
```

### 2. Supabase本地环境设置

#### 安装Supabase CLI

```bash
# 使用npm安装
npm install -g @supabase/cli

# 验证安装
supabase --version
```

#### 启动本地Supabase实例

```bash
# 在项目根目录启动Supabase
supabase start

# 等待所有服务启动完成（首次启动可能需要几分钟）
```

启动成功后，您将看到以下服务信息：

```
supabase local development setup is running.

         API URL: http://127.0.0.1:54321
     GraphQL URL: http://127.0.0.1:54321/graphql/v1
          DB URL: postgresql://postgres:postgres@127.0.0.1:54322/postgres
      Studio URL: http://127.0.0.1:54323
    Inbucket URL: http://127.0.0.1:54324
      JWT secret: super-secret-jwt-token-with-at-least-32-characters-long
        anon key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
service_role key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 验证数据库状态

```bash
# 检查服务状态
supabase status

# 访问Supabase Studio管理界面
# 打开浏览器访问: http://127.0.0.1:54323
```

### 3. 前端环境配置

#### 安装依赖

```bash
# 进入前端目录
cd frontend

# 安装npm依赖
npm install

# 验证安装成功
npm list --depth=0
```

#### 环境变量配置

```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量文件
nano .env.local  # 或使用您喜欢的编辑器
```

在`.env.local`中配置以下变量：

```env
# Supabase配置（本地开发）
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

### 4. 启动开发服务器

```bash
# 在frontend目录下启动开发服务器
npm run dev

# 或者使用其他命令
npm run build    # 构建生产版本
npm run start    # 启动生产服务器
npm run lint     # 代码检查
```

### 5. 验证部署

#### 访问应用

打开浏览器访问：
- **前端应用:** http://localhost:3000
- **Supabase Studio:** http://127.0.0.1:54323
- **邮件查看器:** http://127.0.0.1:54324

#### 运行集成测试

```bash
# 在项目根目录运行测试
cd ..  # 回到项目根目录

# 数据库集成测试
node test-integration.js

# 认证流程测试
node test-auth-flow.js

# MVP功能集成测试
node test-mvp-integration.js
```

所有测试应该显示100%成功率。

## 生产环境部署

### 1. Supabase云端部署

#### 创建Supabase项目

1. 访问 [Supabase](https://supabase.com)
2. 点击"New Project"创建新项目
3. 选择组织和区域
4. 设置数据库密码
5. 等待项目创建完成

#### 运行数据库迁移

1. 在Supabase Dashboard中，进入"SQL Editor"
2. 复制`supabase/migrations/20251007190000_initial_schema.sql`的内容
3. 在SQL编辑器中粘贴并执行
4. 验证所有表和策略创建成功

#### 获取项目配置

在项目设置中获取：
- **Project URL:** `https://your-project-id.supabase.co`
- **Anon Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
- **Service Role Key:** `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 2. 前端部署 (Vercel推荐)

#### 准备部署

```bash
# 确保代码已提交到Git仓库
git add .
git commit -m "Prepare for production deployment"
git push origin main
```

#### Vercel部署步骤

1. 访问 [Vercel](https://vercel.com)
2. 连接GitHub账户
3. 导入SynapseCV仓库
4. 配置项目设置：
   - **Framework Preset:** Next.js
   - **Root Directory:** `frontend`
   - **Build Command:** `npm run build`
   - **Output Directory:** `.next`

#### 环境变量配置

在Vercel项目设置中添加环境变量：

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
NODE_ENV=production
```

#### 部署验证

1. 等待部署完成
2. 访问分配的域名
3. 测试用户注册和登录功能
4. 验证数据库连接正常

### 3. 自定义域名配置 (可选)

#### 在Vercel中配置

1. 在项目设置中点击"Domains"
2. 添加自定义域名
3. 按照指示配置DNS记录
4. 等待SSL证书自动配置

#### DNS配置示例

```
Type: CNAME
Name: www
Value: your-project.vercel.app

Type: A
Name: @
Value: *********** (Vercel IP)
```

## 故障排除

### 常见问题

#### 1. Supabase连接失败

**症状:** 前端无法连接到数据库
**解决方案:**
```bash
# 检查Supabase状态
supabase status

# 重启Supabase服务
supabase stop
supabase start

# 检查环境变量配置
cat frontend/.env.local
```

#### 2. 前端构建失败

**症状:** `npm run build`报错
**解决方案:**
```bash
# 清理依赖和缓存
rm -rf node_modules package-lock.json
npm install

# 检查TypeScript错误
npm run type-check

# 检查ESLint错误
npm run lint
```

#### 3. 认证功能异常

**症状:** 用户无法注册或登录
**解决方案:**
```bash
# 运行认证测试
node test-auth-flow.js

# 检查RLS策略
# 在Supabase Studio中验证策略配置

# 检查邮件确认设置
# 在Supabase Auth设置中禁用邮件确认（开发环境）
```

### 日志查看

#### 本地开发
```bash
# 前端日志
npm run dev  # 在终端查看

# Supabase日志
supabase logs  # 查看所有服务日志
```

#### 生产环境
- **Vercel:** 在项目Dashboard的"Functions"标签查看
- **Supabase:** 在项目Dashboard的"Logs"标签查看

## 性能优化

### 前端优化

```bash
# 分析构建包大小
npm run build
npm run analyze  # 如果配置了bundle analyzer

# 优化图片和静态资源
# 使用Next.js Image组件
# 启用gzip压缩
```

### 数据库优化

```sql
-- 检查查询性能
EXPLAIN ANALYZE SELECT * FROM user_profiles WHERE user_id = 'uuid';

-- 验证索引使用
SELECT * FROM pg_stat_user_indexes WHERE relname = 'master_experiences';
```

## 监控和维护

### 健康检查

创建健康检查端点：

```typescript
// pages/api/health.ts
export default function handler(req, res) {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  });
}
```

### 备份策略

- **Supabase:** 自动每日备份
- **代码:** Git版本控制
- **配置:** 环境变量文档化

## 安全考虑

### 生产环境安全清单

- [ ] 启用HTTPS
- [ ] 配置CORS策略
- [ ] 验证RLS策略
- [ ] 定期更新依赖
- [ ] 监控安全漏洞
- [ ] 配置错误报告

### 环境变量安全

- 不要在代码中硬编码密钥
- 使用不同的密钥用于不同环境
- 定期轮换API密钥
- 限制服务角色密钥的使用

## 支持和联系

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持：<EMAIL>

---

**注意:** 本部署指南会随着项目发展持续更新，请定期查看最新版本。
