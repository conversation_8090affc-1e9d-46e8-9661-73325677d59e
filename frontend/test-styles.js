/**
 * 样式测试脚本
 * 
 * 验证Tailwind CSS样式是否正确生成和应用
 */

const http = require('http');

function fetchCSS() {
  return new Promise((resolve, reject) => {
    const req = http.get('http://localhost:3000/_next/static/css/app/layout.css', (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => resolve(data));
    });
    req.on('error', reject);
  });
}

function testStyles() {
  console.log('🎨 开始测试Tailwind CSS样式...\n');
  
  return fetchCSS()
    .then(css => {
      const tests = [
        { name: '基础布局类', pattern: /\.(flex|grid|block|hidden)\s*\{/g, required: 4 },
        { name: '颜色类', pattern: /\.(bg-blue|text-white|text-gray|bg-white)\s*\{/g, required: 4 },
        { name: '尺寸类', pattern: /\.(w-|h-|max-w|min-h)\s*\{/g, required: 3 },
        { name: '自定义按钮类', pattern: /\.btn-(primary|outline)\s*\{/g, required: 2 },
        { name: '文本对齐类', pattern: /\.(text-center|text-left|text-right)\s*\{/g, required: 2 },
      ];
      
      let allPassed = true;
      
      tests.forEach(test => {
        const matches = css.match(test.pattern);
        const count = matches ? matches.length : 0;
        const passed = count >= test.required;
        const status = passed ? '✅' : '❌';
        
        console.log(`${status} ${test.name}: ${count}/${test.required} 类找到`);
        if (!passed) {
          console.log(`   缺少的类数量: ${test.required - count}`);
          allPassed = false;
        }
      });
      
      console.log('\n📊 测试结果:');
      if (allPassed) {
        console.log('✅ 所有样式测试通过！Tailwind CSS正常工作。');
        return true;
      } else {
        console.log('❌ 部分样式测试失败，需要进一步检查。');
        return false;
      }
    })
    .catch(error => {
      console.error('❌ 测试失败:', error.message);
      return false;
    });
}

// 运行测试
testStyles().then(success => {
  process.exit(success ? 0 : 1);
});
