# SynapseCV Frontend 开发指南

## 开发环境设置

### 1. 环境要求

- Node.js 18.0+
- npm 8.0+ 或 yarn 1.22+
- Git

### 2. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd SynapseCV/frontend

# 安装依赖
npm install

# 复制环境变量文件
cp .env.example .env.local
```

### 3. 环境变量配置

编辑 `.env.local` 文件，配置以下变量：

```env
# 数据库配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# AI服务配置
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key
NEXT_PUBLIC_PERPLEXITY_API_KEY=your_perplexity_api_key

# 支付配置
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=SynapseCV
```

## 开发规范

### 1. 代码风格

- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 使用Prettier进行代码格式化
- 组件使用PascalCase命名
- 文件使用kebab-case命名

### 2. 组件开发规范

#### 基础UI组件

```typescript
// src/components/ui/button.tsx
import * as React from "react";
import { cn } from "@/utils/cn";

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = "default", size = "md", ...props }, ref) => {
    return (
      <button
        className={cn(buttonVariants({ variant, size }), className)}
        ref={ref}
        {...props}
      />
    );
  }
);

Button.displayName = "Button";
export { Button };
```

#### 页面组件

```typescript
// src/app/page.tsx
import { MainLayout } from "@/components/layout/main-layout";

export default function HomePage() {
  return (
    <MainLayout>
      <div className="container mx-auto px-4">
        {/* 页面内容 */}
      </div>
    </MainLayout>
  );
}
```

### 3. 状态管理规范

#### Zustand Store

```typescript
// src/store/auth.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      login: async (email, password) => {
        // 登录逻辑
      },
      logout: async () => {
        // 登出逻辑
      },
    }),
    {
      name: 'auth-storage',
    }
  )
);
```

### 4. 样式开发规范

#### Tailwind CSS v4

项目使用 Tailwind CSS v4，配置方式与 v3 有所不同：

**配置文件变更：**
- `tailwind.config.ts` 中移除了 `content` 配置
- `globals.css` 中使用新的 `@import "tailwindcss"` 语法
- 内容路径配置移至 CSS 文件：`@source "./src/**/*.{js,ts,jsx,tsx,mdx}"`

```typescript
// 使用Tailwind工具类
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">
  <h2 className="text-lg font-semibold text-gray-900">标题</h2>
  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
    按钮
  </button>
</div>
```

#### 自定义样式

```css
/* src/app/globals.css */
@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700;
  }
}
```

### 5. API集成规范

#### Supabase客户端

```typescript
// src/lib/supabase.ts
import { createClient } from '@supabase/supabase-js';

export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

// 使用示例
export async function getExperiences(userId: string) {
  const { data, error } = await supabase
    .from('master_experiences')
    .select('*')
    .eq('user_id', userId);
  
  if (error) throw error;
  return data;
}
```

#### API路由

```typescript
// src/app/api/experiences/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // API逻辑
    return NextResponse.json({ data: [] });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
```

## 测试规范

### 1. 单元测试

```typescript
// tests/components/Button.test.tsx
import { render, screen } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
```

### 2. 集成测试

```typescript
// tests/pages/Login.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import LoginPage from '@/app/login/page';

describe('Login Page', () => {
  it('submits form with valid data', async () => {
    render(<LoginPage />);
    
    fireEvent.change(screen.getByLabelText('邮箱地址'), {
      target: { value: '<EMAIL>' }
    });
    
    fireEvent.click(screen.getByRole('button', { name: '登录' }));
    
    // 断言
  });
});
```

### 3. 运行测试

```bash
# 运行所有测试
npm test

# 运行测试覆盖率
npm run test:coverage

# 运行E2E测试
npm run test:e2e
```

## 性能优化

### 1. 代码分割

```typescript
// 动态导入
import dynamic from 'next/dynamic';

const DynamicComponent = dynamic(() => import('@/components/HeavyComponent'), {
  loading: () => <p>Loading...</p>,
});
```

### 2. 图片优化

```typescript
import Image from 'next/image';

<Image
  src="/hero-image.jpg"
  alt="Hero Image"
  width={800}
  height={600}
  priority
/>
```

### 3. 缓存策略

```typescript
// API路由缓存
export async function GET() {
  const data = await fetch('https://api.example.com/data', {
    next: { revalidate: 3600 } // 1小时缓存
  });
  
  return Response.json(data);
}
```

## 部署指南

### 1. 构建应用

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

### 2. Vercel部署

```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
vercel

# 生产部署
vercel --prod
```

### 3. Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## 调试指南

### 1. 开发工具

- React Developer Tools
- Next.js DevTools
- Tailwind CSS IntelliSense
- TypeScript Language Server

### 2. 常见问题

#### 环境变量问题

```bash
# 检查环境变量
echo $NEXT_PUBLIC_SUPABASE_URL

# 重启开发服务器
npm run dev
```

#### 样式问题

```bash
# 清除Tailwind缓存
rm -rf .next
npm run dev
```

**Tailwind CSS v4 特定问题：**
- 如果样式不生效，检查 `globals.css` 中的 `@import "tailwindcss"` 语法
- 确保 `@source` 指令包含所有需要扫描的文件路径
- 验证 `postcss.config.mjs` 中使用了 `@tailwindcss/postcss` 插件

#### 构建问题

```bash
# 清除构建缓存
rm -rf .next
npm run build
```

### 3. 性能分析

```bash
# 分析包大小
npm run analyze

# 性能测试
npm run lighthouse
```

## 代码审查

### 1. Pull Request检查清单

- [ ] 代码符合项目规范
- [ ] 所有测试通过
- [ ] 无TypeScript错误
- [ ] 无ESLint警告
- [ ] 添加了必要的注释
- [ ] 更新了相关文档

### 2. 代码审查要点

- 代码可读性和可维护性
- 性能影响
- 安全性考虑
- 无障碍性
- 测试覆盖率

## 发布流程

### 1. 版本管理

```bash
# 创建新版本
npm version patch  # 补丁版本
npm version minor  # 次要版本
npm version major  # 主要版本
```

### 2. 发布检查

- [ ] 所有测试通过
- [ ] 构建成功
- [ ] 环境变量配置正确
- [ ] 文档更新完整

### 3. 部署

```bash
# 构建并部署
npm run build
npm run deploy
```

## 故障排除

### 1. 常见错误

#### 模块未找到

```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 类型错误

```bash
# 重新生成类型
npm run type-check
```

#### 样式不生效

```bash
# 清除缓存
rm -rf .next
npm run dev
```

### 2. 性能问题

#### 页面加载慢

- 检查图片优化
- 使用代码分割
- 优化API调用

#### 内存泄漏

- 检查事件监听器
- 清理定时器
- 避免循环引用

## 资源链接

- [Next.js文档](https://nextjs.org/docs)
- [Tailwind CSS文档](https://tailwindcss.com/docs)
- [TypeScript文档](https://www.typescriptlang.org/docs)
- [Zustand文档](https://zustand-demo.pmnd.rs/)
- [Supabase文档](https://supabase.com/docs)
