{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@stripe/stripe-js": "^8.0.0", "@supabase/supabase-js": "^2.74.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.545.0", "next": "15.5.4", "next-pwa": "^5.6.0", "react": "19.1.0", "react-dom": "19.1.0", "stripe": "^19.1.0", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.19", "@testing-library/jest-dom": "^6.9.1", "@testing-library/react": "^16.3.0", "@types/node": "^20.19.19", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.5.4", "jest": "^30.2.0", "jest-environment-jsdom": "^30.2.0", "postcss": "^8.4.49", "tailwindcss": "^4", "typescript": "^5"}}