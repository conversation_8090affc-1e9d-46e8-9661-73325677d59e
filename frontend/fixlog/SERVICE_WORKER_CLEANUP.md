# Service Worker 清理指南

## 问题描述

如果你在访问 `localhost:3000` 时看到页面没有正确渲染，并且浏览器开发者工具显示大量的 500 错误和 Service Worker 相关的请求，这是因为浏览器中注册了过时的 Service Worker。

## 解决方案

### 方法1：使用清理脚本（推荐）

1. 运行清理脚本：
```bash
cd frontend
./cleanup.sh
```

2. 在浏览器中打开清理页面：
```
http://localhost:3000/clear-cache.html
```

3. 点击"清理Service Worker"和"清理所有缓存"按钮

4. 重新启动开发服务器：
```bash
npm run dev
```

### 方法2：手动清理浏览器缓存

1. 按 `F12` 打开浏览器开发者工具
2. 进入 `Application` 标签页
3. 在左侧找到 `Service Workers`，点击并注销所有注册
4. 在左侧找到 `Storage`，点击 `Clear storage` 清理所有数据
5. 刷新页面

### 方法3：使用无痕模式

在无痕模式下访问 `localhost:3000`，这样可以避免 Service Worker 的干扰。

## 预防措施

为了避免将来出现类似问题：

1. **PWA 功能已暂时禁用**：在 `next.config.js` 中注释掉了 PWA 配置
2. **清理脚本**：`cleanup.sh` 可以随时运行来清理缓存
3. **清理页面**：`clear-cache.html` 可以在浏览器中直接使用

## 重新启用 PWA（可选）

如果将来需要重新启用 PWA 功能：

1. 取消注释 `next.config.js` 中的 PWA 配置
2. 确保正确配置环境变量
3. 重新构建项目

## 故障排除

如果问题仍然存在：

1. 完全关闭浏览器并重新打开
2. 清除浏览器所有数据（设置 > 隐私和安全 > 清除浏览数据）
3. 尝试使用不同的浏览器
4. 检查是否有其他扩展程序干扰

## 技术说明

Service Worker 是浏览器中的后台脚本，用于缓存资源和处理网络请求。当 Service Worker 配置不正确或缓存过期时，可能会导致页面无法正确加载。通过清理这些缓存和注册，可以解决渲染问题。
