/**
 * Supabase数据库类型定义
 * 
 * 根据数据库设计文档定义的TypeScript类型
 * 与后端数据库表结构保持一致
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          user_id: string
          full_name: string | null
          title: string | null
          contact_info: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          full_name?: string | null
          title?: string | null
          contact_info?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          user_id?: string
          full_name?: string | null
          title?: string | null
          contact_info?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_profiles_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      master_experiences: {
        Row: {
          exp_id: string
          user_id: string
          company_name: string
          role_title: string
          start_date: string  // DATE类型在PostgreSQL中返回为字符串
          end_date: string | null
          is_current: boolean
          raw_facts: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          exp_id?: string
          user_id: string
          company_name: string
          role_title: string
          start_date: string
          end_date?: string | null
          is_current?: boolean
          raw_facts: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          exp_id?: string
          user_id?: string
          company_name?: string
          role_title?: string
          start_date?: string
          end_date?: string | null
          is_current?: boolean
          raw_facts?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "master_experiences_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      optimized_content: {
        Row: {
          opt_id: string
          exp_id: string
          target_focus: string
          optimized_bullets: Json
          ai_model: string
          created_at: string
          updated_at: string
        }
        Insert: {
          opt_id?: string
          exp_id: string
          target_focus: string
          optimized_bullets: Json
          ai_model: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          opt_id?: string
          exp_id?: string
          target_focus?: string
          optimized_bullets?: Json
          ai_model?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "optimized_content_exp_id_fkey"
            columns: ["exp_id"]
            isOneToOne: false
            referencedRelation: "master_experiences"
            referencedColumns: ["exp_id"]
          }
        ]
      }
      resume_versions: {
        Row: {
          version_id: string
          user_id: string
          version_name: string
          template_id: string
          version_config: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          version_id?: string
          user_id: string
          version_name: string
          template_id: string
          version_config: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          version_id?: string
          user_id?: string
          version_name?: string
          template_id?: string
          version_config?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "resume_versions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      n8n_automation_log: {
        Row: {
          log_id: string
          user_id: string
          event_type: string
          payload: Json
          status: string
          created_at: string
        }
        Insert: {
          log_id?: string
          user_id: string
          event_type: string
          payload: Json
          status: string
          created_at?: string
        }
        Update: {
          log_id?: string
          user_id?: string
          event_type?: string
          payload?: Json
          status?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "n8n_automation_log_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
