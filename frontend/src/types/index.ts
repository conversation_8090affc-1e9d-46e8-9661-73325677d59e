/**
 * SynapseCV Frontend Type Definitions
 * 
 * 定义前端应用中使用的所有TypeScript类型
 * 包括用户、简历、订阅等核心业务实体的类型定义
 */

// 用户相关类型
export interface User {
  user_id: string;
  email: string;
  full_name?: string;
  title?: string;
  contact_info?: ContactInfo;
  subscription_status: SubscriptionStatus;
  created_at: string;
  updated_at: string;
}

export interface ContactInfo {
  phone?: string;
  location?: string;
  linkedin?: string;
  github?: string;
  website?: string;
}

export type SubscriptionStatus = 'trial' | 'active' | 'cancelled' | 'expired';

// 简历经历相关类型
export interface MasterExperience {
  exp_id: string;
  user_id: string;
  company_name: string;
  role_title: string;
  start_date: string;
  end_date?: string;
  is_current: boolean;
  raw_facts: RawFacts;
  created_at: string;
  updated_at: string;
}

export interface RawFacts {
  description: string;
  technologies: string[];
  quantified_data?: string;
  achievements?: string[];
  responsibilities?: string[];
}

export interface OptimizedContent {
  opt_id: string;
  exp_id: string;
  target_focus: string;
  optimized_bullets: string[];
  ai_model: string;
  created_at: string;
  updated_at: string;
}

// 简历版本相关类型
export interface ResumeVersion {
  version_id: string;
  user_id: string;
  version_name: string;
  template_id: string;
  version_config: VersionConfig;
  created_at: string;
  updated_at: string;
}

export interface VersionConfig {
  included_experience_ids: string[];
  display_order: string[];
  custom_settings?: Record<string, unknown>;
}

// 订阅相关类型
export interface Subscription {
  subscription_id: string;
  user_id: string;
  stripe_subscription_id: string;
  status: SubscriptionStatus;
  current_period_start: string;
  current_period_end: string;
  created_at: string;
  updated_at: string;
}

// API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: number;
    message: string;
    details?: unknown;
  };
}

// 表单相关类型
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  full_name?: string;
}

export interface ExperienceForm {
  company_name: string;
  role_title: string;
  start_date: string;
  end_date?: string;
  is_current: boolean;
  description: string;
  technologies: string[];
  quantified_data?: string;
  achievements?: string[];
  responsibilities?: string[];
}

// UI状态类型
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

export interface ErrorState {
  hasError: boolean;
  message?: string;
  code?: number;
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// 路由参数类型
export interface PageParams {
  params: {
    id?: string;
    [key: string]: string | undefined;
  };
  searchParams?: {
    [key: string]: string | undefined;
  };
}

// 工具类型
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
