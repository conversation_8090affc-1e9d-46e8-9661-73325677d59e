/**
 * 主布局组件
 * 
 * 提供页面的整体布局结构，包括头部、主体内容和底部
 * 支持响应式设计和无障碍性
 */

"use client";

import * as React from "react";
import { Header } from "./header";
import { Footer } from "./footer";
import { cn } from "@/utils/cn";

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

/**
 * 主布局组件
 * 
 * @param children - 页面内容
 * @param className - 额外的CSS类名
 * @param showHeader - 是否显示头部
 * @param showFooter - 是否显示底部
 */
export function MainLayout({ 
  children, 
  className,
  showHeader = true,
  showFooter = true 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {showHeader && <Header />}
      
      <main className={cn("flex-1", className)}>
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
}
