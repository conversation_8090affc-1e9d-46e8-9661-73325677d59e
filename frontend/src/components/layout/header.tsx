/**
 * 页面头部组件
 * 
 * 包含导航栏、用户菜单、订阅状态等
 * 响应式设计，支持移动端和桌面端
 */

"use client";

import * as React from "react";
import Link from "next/link";
import { useAuthStore } from "@/store/auth";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading";
import { cn } from "@/utils/cn";

interface HeaderProps {
  className?: string;
}

/**
 * 页面头部组件
 * 
 * @param className - 额外的CSS类名
 */
export function Header({ className }: HeaderProps) {
  const { user, isAuthenticated, isLoading, logout } = useAuthStore();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <header className={cn("bg-white shadow-sm border-b", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo和品牌 */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <span className="text-xl font-bold text-gray-900">SynapseCV</span>
            </Link>
          </div>

          {/* 导航菜单 */}
          <nav className="hidden md:flex items-center space-x-8">
            {isAuthenticated && (
              <>
                <Link
                  href="/dashboard"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  控制台
                </Link>
                <Link
                  href="/experiences"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  经历管理
                </Link>
                <Link
                  href="/versions"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  简历版本
                </Link>
                <Link
                  href="/templates"
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  模板
                </Link>
              </>
            )}
          </nav>

          {/* 用户操作区域 */}
          <div className="flex items-center space-x-4">
            {isLoading ? (
              <LoadingSpinner size="sm" />
            ) : isAuthenticated && user ? (
              <div className="flex items-center space-x-4">
                {/* 订阅状态指示器 */}
                <div className="hidden sm:flex items-center space-x-2">
                  <div
                    className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      user.subscription_status === "active"
                        ? "bg-green-100 text-green-800"
                        : user.subscription_status === "trial"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    )}
                  >
                    {user.subscription_status === "active"
                      ? "付费用户"
                      : user.subscription_status === "trial"
                      ? "试用中"
                      : "免费用户"}
                  </div>
                </div>

                {/* 用户菜单 */}
                <div className="relative group">
                  <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium">
                        {user.full_name?.[0] || user.email[0].toUpperCase()}
                      </span>
                    </div>
                    <span className="hidden sm:block text-sm">
                      {user.full_name || user.email}
                    </span>
                  </button>

                  {/* 下拉菜单 */}
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      个人资料
                    </Link>
                    <Link
                      href="/settings"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      设置
                    </Link>
                    <Link
                      href="/billing"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      订阅管理
                    </Link>
                    <hr className="my-1" />
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      退出登录
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/login">
                  <Button variant="ghost" size="sm">
                    登录
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="sm">
                    注册
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}
