/**
 * 加载组件
 * 
 * 提供多种加载状态的UI组件
 * 包括旋转加载器、骨架屏等
 */

import * as React from "react";
import { cn } from "@/utils/cn";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

/**
 * 旋转加载器组件
 * 
 * @param size - 加载器尺寸
 * @param className - 额外的CSS类名
 */
export function LoadingSpinner({ size = "md", className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8",
    lg: "h-12 w-12"
  };

  return (
    <div
      className={cn(
        "animate-spin rounded-full border-2 border-gray-300 border-t-blue-600",
        sizeClasses[size],
        className
      )}
      role="status"
      aria-label="加载中"
    >
      <span className="sr-only">加载中...</span>
    </div>
  );
}

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * 加载遮罩组件
 * 
 * @param isLoading - 是否显示加载状态
 * @param message - 加载提示信息
 * @param children - 子组件内容
 * @param className - 额外的CSS类名
 */
export function LoadingOverlay({ 
  isLoading, 
  message = "加载中...", 
  children, 
  className 
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {isLoading && (
        <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center space-y-2">
            <LoadingSpinner size="lg" />
            <p className="text-sm text-gray-600">{message}</p>
          </div>
        </div>
      )}
    </div>
  );
}

interface SkeletonProps {
  className?: string;
  height?: string;
  width?: string;
}

/**
 * 骨架屏组件
 * 
 * @param className - 额外的CSS类名
 * @param height - 高度
 * @param width - 宽度
 */
export function Skeleton({ className, height, width }: SkeletonProps) {
  return (
    <div
      className={cn(
        "animate-pulse bg-gray-200 rounded",
        height || "h-4",
        width || "w-full",
        className
      )}
    />
  );
}

interface LoadingCardProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
}

/**
 * 加载卡片组件
 * 
 * @param isLoading - 是否显示加载状态
 * @param children - 子组件内容
 * @param className - 额外的CSS类名
 */
export function LoadingCard({ isLoading, children, className }: LoadingCardProps) {
  if (isLoading) {
    return (
      <div className={cn("p-6 bg-white rounded-lg shadow", className)}>
        <div className="space-y-4">
          <Skeleton height="h-6" width="w-3/4" />
          <Skeleton height="h-4" width="w-full" />
          <Skeleton height="h-4" width="w-5/6" />
          <Skeleton height="h-4" width="w-4/6" />
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
