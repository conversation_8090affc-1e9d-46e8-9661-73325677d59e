/**
 * 认证提供者组件
 * 
 * 在应用启动时初始化认证状态，监听认证变化
 */

'use client';

import { useEffect, useState } from 'react';
import { useAuthStore } from '@/store/auth';
import { supabase } from '@/lib/supabase';
import { LoadingSpinner } from '@/components/ui/loading';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * 认证提供者组件
 *
 * 在应用根部使用，负责初始化和监听认证状态
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const { checkAuth, setUser, isLoading } = useAuthStore();
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const initializeAuth = async () => {
      try {
        // 初始化认证状态
        await checkAuth();
      } catch (error) {
        console.error('认证初始化失败:', error);
      } finally {
        if (isMounted) {
          setIsInitializing(false);
        }
      }
    };

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('认证状态变化:', event, session?.user?.email);

        if (!isMounted) return;

        switch (event) {
          case 'INITIAL_SESSION':
            // 初始会话加载
            if (session?.user) {
              await checkAuth();
            } else {
              setUser(null);
            }
            break;

          case 'SIGNED_IN':
            // 用户登录
            if (session?.user) {
              await checkAuth();
            }
            break;

          case 'SIGNED_OUT':
            // 用户登出
            setUser(null);
            break;

          case 'TOKEN_REFRESHED':
            // Token刷新
            if (session?.user) {
              await checkAuth();
            }
            break;

          case 'USER_UPDATED':
            // 用户信息更新
            if (session?.user) {
              await checkAuth();
            }
            break;

          default:
            break;
        }
      }
    );

    // 开始初始化
    initializeAuth();

    // 清理函数
    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, [checkAuth, setUser]);

  // 显示初始化加载状态
  if (isInitializing || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">正在初始化应用...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
