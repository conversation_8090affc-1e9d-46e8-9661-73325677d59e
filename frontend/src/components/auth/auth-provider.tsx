/**
 * 认证提供者组件
 * 
 * 在应用启动时初始化认证状态，监听认证变化
 */

'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { supabase } from '@/lib/supabase';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * 认证提供者组件
 * 
 * 在应用根部使用，负责初始化和监听认证状态
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const { checkAuth, setUser } = useAuthStore();

  useEffect(() => {
    // 初始化认证状态
    checkAuth();

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('认证状态变化:', event, session?.user?.email);

        switch (event) {
          case 'SIGNED_IN':
            // 用户登录
            if (session?.user) {
              await checkAuth();
            }
            break;

          case 'SIGNED_OUT':
            // 用户登出
            setUser(null);
            break;

          case 'TOKEN_REFRESHED':
            // Token刷新
            if (session?.user) {
              await checkAuth();
            }
            break;

          case 'USER_UPDATED':
            // 用户信息更新
            if (session?.user) {
              await checkAuth();
            }
            break;

          default:
            break;
        }
      }
    );

    // 清理监听器
    return () => {
      subscription.unsubscribe();
    };
  }, [checkAuth, setUser]);

  return <>{children}</>;
}
