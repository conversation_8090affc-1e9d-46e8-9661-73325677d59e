/**
 * 认证保护组件
 * 
 * 用于保护需要登录才能访问的页面
 * 如果用户未登录，自动重定向到登录页面
 */

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/auth';
import { LoadingSpinner } from '@/components/ui/loading';

interface AuthGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

/**
 * 认证保护组件
 * 
 * @param children - 需要保护的子组件
 * @param redirectTo - 未登录时重定向的路径，默认为 '/login'
 * @param fallback - 加载时显示的组件
 */
export function AuthGuard({ 
  children, 
  redirectTo = '/login',
  fallback 
}: AuthGuardProps) {
  const router = useRouter();
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await checkAuth();
      } catch (error) {
        console.error('认证检查失败:', error);
      } finally {
        setIsChecking(false);
      }
    };

    initAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isChecking && !isLoading && !isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isChecking, isLoading, isAuthenticated, router, redirectTo]);

  // 显示加载状态
  if (isChecking || isLoading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    );
  }

  // 如果未认证，显示空内容（即将重定向）
  if (!isAuthenticated) {
    return null;
  }

  // 认证通过，显示受保护的内容
  return <>{children}</>;
}

/**
 * 高阶组件：为页面添加认证保护
 * 
 * @param WrappedComponent - 需要保护的页面组件
 * @param options - 保护选项
 */
export function withAuthGuard<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options?: {
    redirectTo?: string;
    fallback?: React.ReactNode;
  }
) {
  const AuthGuardedComponent = (props: P) => {
    return (
      <AuthGuard 
        redirectTo={options?.redirectTo}
        fallback={options?.fallback}
      >
        <WrappedComponent {...props} />
      </AuthGuard>
    );
  };

  AuthGuardedComponent.displayName = `withAuthGuard(${WrappedComponent.displayName || WrappedComponent.name})`;

  return AuthGuardedComponent;
}

/**
 * 反向认证保护组件
 * 
 * 用于保护登录页面等，已登录用户访问时重定向到dashboard
 */
export function GuestGuard({ 
  children, 
  redirectTo = '/dashboard' 
}: {
  children: React.ReactNode;
  redirectTo?: string;
}) {
  const router = useRouter();
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await checkAuth();
      } catch (error) {
        console.error('认证检查失败:', error);
      } finally {
        setIsChecking(false);
      }
    };

    initAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isChecking && !isLoading && isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isChecking, isLoading, isAuthenticated, router, redirectTo]);

  // 显示加载状态
  if (isChecking || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">正在验证登录状态...</p>
        </div>
      </div>
    );
  }

  // 如果已认证，显示空内容（即将重定向）
  if (isAuthenticated) {
    return null;
  }

  // 未认证，显示内容
  return <>{children}</>;
}
