/**
 * 数据库操作函数
 * 
 * 提供与Supabase数据库交互的具体实现
 * 包括用户资料、经历、优化内容、版本等CRUD操作
 */

import { supabase } from './supabase';
import { Database } from '@/types/database';

// 类型别名，简化代码
type UserProfile = Database['public']['Tables']['user_profiles']['Row'];
type UserProfileInsert = Database['public']['Tables']['user_profiles']['Insert'];
type UserProfileUpdate = Database['public']['Tables']['user_profiles']['Update'];

type MasterExperience = Database['public']['Tables']['master_experiences']['Row'];
type MasterExperienceInsert = Database['public']['Tables']['master_experiences']['Insert'];
type MasterExperienceUpdate = Database['public']['Tables']['master_experiences']['Update'];

type OptimizedContent = Database['public']['Tables']['optimized_content']['Row'];
type OptimizedContentInsert = Database['public']['Tables']['optimized_content']['Insert'];
type OptimizedContentUpdate = Database['public']['Tables']['optimized_content']['Update'];

type ResumeVersion = Database['public']['Tables']['resume_versions']['Row'];
type ResumeVersionInsert = Database['public']['Tables']['resume_versions']['Insert'];
type ResumeVersionUpdate = Database['public']['Tables']['resume_versions']['Update'];

// =====================================================
// 用户资料操作
// =====================================================

/**
 * 获取用户资料
 * @param userId - 用户ID
 * @returns 用户资料信息
 */
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  const { data, error } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }

  return data;
}

/**
 * 创建用户资料
 * @param profile - 用户资料数据
 * @returns 创建结果
 */
export async function createUserProfile(profile: UserProfileInsert): Promise<UserProfile | null> {
  const { data, error } = await supabase
    .from('user_profiles')
    .insert(profile)
    .select()
    .single();

  if (error) {
    console.error('Error creating user profile:', error);
    console.error('Profile data:', profile);
    // 抛出错误，让调用者知道失败了
    throw new Error(`Failed to create user profile: ${error.message}`);
  }

  return data;
}

/**
 * 更新用户资料
 * @param userId - 用户ID
 * @param updates - 更新数据
 * @returns 更新结果
 */
export async function updateUserProfile(
  userId: string, 
  updates: UserProfileUpdate
): Promise<UserProfile | null> {
  const { data, error } = await supabase
    .from('user_profiles')
    .update(updates)
    .eq('user_id', userId)
    .select()
    .single();

  if (error) {
    console.error('Error updating user profile:', error);
    return null;
  }

  return data;
}

// =====================================================
// 经历操作
// =====================================================

/**
 * 获取用户的所有经历
 * @param userId - 用户ID
 * @returns 经历列表
 */
export async function getUserExperiences(userId: string): Promise<MasterExperience[]> {
  const { data, error } = await supabase
    .from('master_experiences')
    .select('*')
    .eq('user_id', userId)
    .order('start_date', { ascending: false });

  if (error) {
    console.error('Error fetching user experiences:', error);
    return [];
  }

  return data || [];
}

/**
 * 获取单个经历
 * @param expId - 经历ID
 * @returns 经历信息
 */
export async function getExperience(expId: string): Promise<MasterExperience | null> {
  const { data, error } = await supabase
    .from('master_experiences')
    .select('*')
    .eq('exp_id', expId)
    .single();

  if (error) {
    console.error('Error fetching experience:', error);
    return null;
  }

  return data;
}

/**
 * 创建新经历
 * @param experience - 经历数据
 * @returns 创建结果
 */
export async function createExperience(experience: MasterExperienceInsert): Promise<MasterExperience | null> {
  const { data, error } = await supabase
    .from('master_experiences')
    .insert(experience)
    .select()
    .single();

  if (error) {
    console.error('Error creating experience:', error);
    return null;
  }

  return data;
}

/**
 * 更新经历
 * @param expId - 经历ID
 * @param updates - 更新数据
 * @returns 更新结果
 */
export async function updateExperience(
  expId: string, 
  updates: MasterExperienceUpdate
): Promise<MasterExperience | null> {
  const { data, error } = await supabase
    .from('master_experiences')
    .update(updates)
    .eq('exp_id', expId)
    .select()
    .single();

  if (error) {
    console.error('Error updating experience:', error);
    return null;
  }

  return data;
}

/**
 * 删除经历
 * @param expId - 经历ID
 * @returns 删除结果
 */
export async function deleteExperience(expId: string): Promise<boolean> {
  const { error } = await supabase
    .from('master_experiences')
    .delete()
    .eq('exp_id', expId);

  if (error) {
    console.error('Error deleting experience:', error);
    return false;
  }

  return true;
}

// =====================================================
// 优化内容操作
// =====================================================

/**
 * 获取经历的优化内容
 * @param expId - 经历ID
 * @returns 优化内容列表
 */
export async function getOptimizedContent(expId: string): Promise<OptimizedContent[]> {
  const { data, error } = await supabase
    .from('optimized_content')
    .select('*')
    .eq('exp_id', expId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching optimized content:', error);
    return [];
  }

  return data || [];
}

/**
 * 获取特定目标的优化内容
 * @param expId - 经历ID
 * @param targetFocus - 目标焦点
 * @returns 优化内容
 */
export async function getOptimizedContentByTarget(
  expId: string, 
  targetFocus: string
): Promise<OptimizedContent | null> {
  const { data, error } = await supabase
    .from('optimized_content')
    .select('*')
    .eq('exp_id', expId)
    .eq('target_focus', targetFocus)
    .single();

  if (error) {
    console.error('Error fetching optimized content by target:', error);
    return null;
  }

  return data;
}

/**
 * 创建优化内容
 * @param content - 优化内容数据
 * @returns 创建结果
 */
export async function createOptimizedContent(content: OptimizedContentInsert): Promise<OptimizedContent | null> {
  const { data, error } = await supabase
    .from('optimized_content')
    .insert(content)
    .select()
    .single();

  if (error) {
    console.error('Error creating optimized content:', error);
    return null;
  }

  return data;
}

/**
 * 更新优化内容
 * @param optId - 优化内容ID
 * @param updates - 更新数据
 * @returns 更新结果
 */
export async function updateOptimizedContent(
  optId: string, 
  updates: OptimizedContentUpdate
): Promise<OptimizedContent | null> {
  const { data, error } = await supabase
    .from('optimized_content')
    .update(updates)
    .eq('opt_id', optId)
    .select()
    .single();

  if (error) {
    console.error('Error updating optimized content:', error);
    return null;
  }

  return data;
}

// =====================================================
// 简历版本操作
// =====================================================

/**
 * 获取用户的所有简历版本
 * @param userId - 用户ID
 * @returns 版本列表
 */
export async function getUserResumeVersions(userId: string): Promise<ResumeVersion[]> {
  const { data, error } = await supabase
    .from('resume_versions')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching resume versions:', error);
    return [];
  }

  return data || [];
}

/**
 * 获取单个简历版本
 * @param versionId - 版本ID
 * @returns 版本信息
 */
export async function getResumeVersion(versionId: string): Promise<ResumeVersion | null> {
  const { data, error } = await supabase
    .from('resume_versions')
    .select('*')
    .eq('version_id', versionId)
    .single();

  if (error) {
    console.error('Error fetching resume version:', error);
    return null;
  }

  return data;
}

/**
 * 创建简历版本
 * @param version - 版本数据
 * @returns 创建结果
 */
export async function createResumeVersion(version: ResumeVersionInsert): Promise<ResumeVersion | null> {
  const { data, error } = await supabase
    .from('resume_versions')
    .insert(version)
    .select()
    .single();

  if (error) {
    console.error('Error creating resume version:', error);
    return null;
  }

  return data;
}

/**
 * 更新简历版本
 * @param versionId - 版本ID
 * @param updates - 更新数据
 * @returns 更新结果
 */
export async function updateResumeVersion(
  versionId: string, 
  updates: ResumeVersionUpdate
): Promise<ResumeVersion | null> {
  const { data, error } = await supabase
    .from('resume_versions')
    .update(updates)
    .eq('version_id', versionId)
    .select()
    .single();

  if (error) {
    console.error('Error updating resume version:', error);
    return null;
  }

  return data;
}

/**
 * 删除简历版本
 * @param versionId - 版本ID
 * @returns 删除结果
 */
export async function deleteResumeVersion(versionId: string): Promise<boolean> {
  const { error } = await supabase
    .from('resume_versions')
    .delete()
    .eq('version_id', versionId);

  if (error) {
    console.error('Error deleting resume version:', error);
    return false;
  }

  return true;
}

// =====================================================
// 搜索和查询操作
// =====================================================

/**
 * 搜索用户经历
 * @param userId - 用户ID
 * @param searchTerm - 搜索关键词
 * @returns 搜索结果
 */
export async function searchUserExperiences(
  userId: string, 
  searchTerm: string
): Promise<MasterExperience[]> {
  const { data, error } = await supabase
    .from('master_experiences')
    .select('*')
    .eq('user_id', userId)
    .or(`company_name.ilike.%${searchTerm}%,role_title.ilike.%${searchTerm}%,raw_facts->>description.ilike.%${searchTerm}%`)
    .order('start_date', { ascending: false });

  if (error) {
    console.error('Error searching experiences:', error);
    return [];
  }

  return data || [];
}

/**
 * 获取用户的完整简历数据
 * @param userId - 用户ID
 * @returns 完整的简历数据
 */
export async function getUserCompleteResumeData(userId: string) {
  // 获取用户资料
  const profile = await getUserProfile(userId);
  
  // 获取经历列表
  const experiences = await getUserExperiences(userId);
  
  // 获取所有优化内容
  const optimizedContentPromises = experiences.map(exp => 
    getOptimizedContent(exp.exp_id)
  );
  const allOptimizedContent = await Promise.all(optimizedContentPromises);
  
  // 获取简历版本
  const versions = await getUserResumeVersions(userId);

  return {
    profile,
    experiences,
    optimizedContent: allOptimizedContent.flat(),
    versions
  };
}
