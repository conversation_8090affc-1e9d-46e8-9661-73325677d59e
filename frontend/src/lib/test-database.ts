/**
 * 数据库集成测试工具
 * 
 * 用于测试前端与Supabase数据库的集成
 * 验证CRUD操作是否正常工作
 */

import { supabase } from './supabase';
import { 
  createUserProfile, 
  getUserProfile, 
  updateUserProfile,
  createExperience,
  getUserExperiences,
  updateExperience,
  deleteExperience
} from './database';

export interface TestResult {
  test: string;
  success: boolean;
  message: string;
  data?: any;
}

/**
 * 测试数据库连接
 */
export async function testDatabaseConnection(): Promise<TestResult> {
  try {
    const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
    
    if (error) {
      return {
        test: 'Database Connection',
        success: false,
        message: `连接失败: ${error.message}`
      };
    }

    return {
      test: 'Database Connection',
      success: true,
      message: '数据库连接成功'
    };
  } catch (error: any) {
    return {
      test: 'Database Connection',
      success: false,
      message: `连接异常: ${error.message}`
    };
  }
}

/**
 * 测试用户认证
 */
export async function testAuthentication(): Promise<TestResult> {
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      return {
        test: 'Authentication',
        success: false,
        message: `认证失败: ${error.message}`
      };
    }

    if (!user) {
      return {
        test: 'Authentication',
        success: false,
        message: '用户未登录'
      };
    }

    return {
      test: 'Authentication',
      success: true,
      message: '用户认证成功',
      data: { userId: user.id, email: user.email }
    };
  } catch (error: any) {
    return {
      test: 'Authentication',
      success: false,
      message: `认证异常: ${error.message}`
    };
  }
}

/**
 * 测试用户资料CRUD操作
 */
export async function testUserProfileCRUD(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return [{
        test: 'User Profile CRUD',
        success: false,
        message: '用户未登录，无法测试用户资料操作'
      }];
    }

    // 测试读取用户资料
    try {
      const profile = await getUserProfile(user.id);
      results.push({
        test: 'Read User Profile',
        success: true,
        message: '读取用户资料成功',
        data: profile
      });
    } catch (error: any) {
      results.push({
        test: 'Read User Profile',
        success: false,
        message: `读取失败: ${error.message}`
      });
    }

    // 测试更新用户资料
    try {
      const updateData = {
        full_name: `测试用户_${Date.now()}`,
        title: '测试工程师',
        contact_info: { test: true }
      };
      
      const updatedProfile = await updateUserProfile(user.id, updateData);
      results.push({
        test: 'Update User Profile',
        success: !!updatedProfile,
        message: updatedProfile ? '更新用户资料成功' : '更新用户资料失败',
        data: updatedProfile
      });
    } catch (error: any) {
      results.push({
        test: 'Update User Profile',
        success: false,
        message: `更新失败: ${error.message}`
      });
    }

  } catch (error: any) {
    results.push({
      test: 'User Profile CRUD',
      success: false,
      message: `测试异常: ${error.message}`
    });
  }

  return results;
}

/**
 * 测试经历CRUD操作
 */
export async function testExperienceCRUD(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  let createdExpId: string | null = null;
  
  try {
    // 获取当前用户
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return [{
        test: 'Experience CRUD',
        success: false,
        message: '用户未登录，无法测试经历操作'
      }];
    }

    // 测试创建经历
    try {
      const testExperience = {
        user_id: user.id,
        experience_type: 'work' as const,
        company: '测试公司',
        position: '测试职位',
        start_date: '2023-01-01',
        end_date: '2023-12-31',
        location: '测试地点',
        description: '这是一个测试经历',
        technologies: ['JavaScript', 'TypeScript'],
        raw_facts: {
          achievements: ['测试成就1', '测试成就2'],
          responsibilities: ['测试职责1', '测试职责2'],
          metrics: ['提升效率50%', '减少bug数量30%']
        }
      };

      const createdExp = await createExperience(testExperience);
      if (createdExp) {
        createdExpId = createdExp.exp_id;
        results.push({
          test: 'Create Experience',
          success: true,
          message: '创建经历成功',
          data: createdExp
        });
      } else {
        results.push({
          test: 'Create Experience',
          success: false,
          message: '创建经历失败'
        });
      }
    } catch (error: any) {
      results.push({
        test: 'Create Experience',
        success: false,
        message: `创建失败: ${error.message}`
      });
    }

    // 测试读取经历列表
    try {
      const experiences = await getUserExperiences(user.id);
      results.push({
        test: 'Read Experiences',
        success: true,
        message: `读取经历列表成功，共${experiences.length}条记录`,
        data: { count: experiences.length }
      });
    } catch (error: any) {
      results.push({
        test: 'Read Experiences',
        success: false,
        message: `读取失败: ${error.message}`
      });
    }

    // 测试更新经历
    if (createdExpId) {
      try {
        const updateData = {
          position: '更新后的测试职位',
          description: '这是更新后的测试经历描述'
        };
        
        const updatedExp = await updateExperience(createdExpId, updateData);
        results.push({
          test: 'Update Experience',
          success: !!updatedExp,
          message: updatedExp ? '更新经历成功' : '更新经历失败',
          data: updatedExp
        });
      } catch (error: any) {
        results.push({
          test: 'Update Experience',
          success: false,
          message: `更新失败: ${error.message}`
        });
      }
    }

    // 测试删除经历
    if (createdExpId) {
      try {
        const deleted = await deleteExperience(createdExpId);
        results.push({
          test: 'Delete Experience',
          success: deleted,
          message: deleted ? '删除经历成功' : '删除经历失败'
        });
      } catch (error: any) {
        results.push({
          test: 'Delete Experience',
          success: false,
          message: `删除失败: ${error.message}`
        });
      }
    }

  } catch (error: any) {
    results.push({
      test: 'Experience CRUD',
      success: false,
      message: `测试异常: ${error.message}`
    });
  }

  return results;
}

/**
 * 运行所有数据库测试
 */
export async function runAllDatabaseTests(): Promise<TestResult[]> {
  const results: TestResult[] = [];
  
  // 测试数据库连接
  results.push(await testDatabaseConnection());
  
  // 测试用户认证
  results.push(await testAuthentication());
  
  // 测试用户资料CRUD
  const profileResults = await testUserProfileCRUD();
  results.push(...profileResults);
  
  // 测试经历CRUD
  const experienceResults = await testExperienceCRUD();
  results.push(...experienceResults);
  
  return results;
}

/**
 * 格式化测试结果
 */
export function formatTestResults(results: TestResult[]): string {
  let output = '=== 数据库集成测试结果 ===\n\n';
  
  const passed = results.filter(r => r.success).length;
  const total = results.length;
  
  output += `总测试数: ${total}\n`;
  output += `通过: ${passed}\n`;
  output += `失败: ${total - passed}\n`;
  output += `成功率: ${((passed / total) * 100).toFixed(1)}%\n\n`;
  
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    output += `${index + 1}. ${status} ${result.test}\n`;
    output += `   ${result.message}\n`;
    if (result.data) {
      output += `   数据: ${JSON.stringify(result.data, null, 2)}\n`;
    }
    output += '\n';
  });
  
  return output;
}
