/**
 * Supabase客户端配置
 * 
 * 配置Supabase客户端，用于与后端数据库交互
 * 包括认证、数据查询等功能
 */

import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

// 从环境变量获取Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// 开发环境验证
if (process.env.NODE_ENV === 'development' && (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)) {
  console.warn('Missing Supabase environment variables. Please check your .env.local file.');
}

/**
 * Supabase客户端实例
 * 用于与Supabase数据库进行交互
 *
 * 配置说明：
 * - autoRefreshToken: 自动刷新访问令牌
 * - persistSession: 在localStorage中持久化会话
 * - detectSessionInUrl: 检测URL中的会话信息（用于邮件确认等）
 * - flowType: 使用PKCE流程提高安全性
 */
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
});

/**
 * 获取当前会话
 * @returns 当前会话信息
 */
export async function getCurrentSession() {
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    console.error('Error getting current session:', error);
    return null;
  }

  return session;
}

/**
 * 获取当前用户
 * @returns 当前登录的用户信息
 */
export async function getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error) {
    console.error('Error getting current user:', error);
    return null;
  }

  return user;
}

/**
 * 用户登录
 * @param email - 用户邮箱
 * @param password - 用户密码
 * @returns 登录结果
 */
export async function signIn(email: string, password: string) {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password
  });
  
  return { data, error };
}

/**
 * 用户注册
 * @param email - 用户邮箱
 * @param password - 用户密码
 * @returns 注册结果
 */
export async function signUp(email: string, password: string) {
  const { data, error } = await supabase.auth.signUp({
    email,
    password
  });
  
  return { data, error };
}

/**
 * 用户登出
 * @returns 登出结果
 */
export async function signOut() {
  const { error } = await supabase.auth.signOut();
  return { error };
}

/**
 * 重置密码
 * @param email - 用户邮箱
 * @returns 重置结果
 */
export async function resetPassword(email: string) {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`
  });
  
  return { data, error };
}
