/**
 * 简历数据状态管理
 * 
 * 管理用户的简历经历、优化内容和版本信息
 * 支持"一改全改"机制，确保数据一致性
 */

import { create } from 'zustand';
import { MasterExperience, OptimizedContent, ResumeVersion, RawFacts, VersionConfig } from '@/types';
import { Json } from '@/types/database';
import {
  getUserExperiences,
  createExperience,
  updateExperience,
  deleteExperience,
  getOptimizedContent,
  // createOptimizedContent,
  // updateOptimizedContent,
  getUserResumeVersions,
  // createResumeVersion,
  // updateResumeVersion,
  // deleteResumeVersion
} from '@/lib/database';
import { useAuthStore } from './auth';

interface ResumeState {
  // 状态
  experiences: MasterExperience[];
  optimizedContent: OptimizedContent[];
  versions: ResumeVersion[];
  isLoading: boolean;
  error: string | null;

  // 经历管理
  addExperience: (experience: Omit<MasterExperience, 'exp_id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updateExperience: (expId: string, updates: Partial<MasterExperience>) => Promise<boolean>;
  deleteExperience: (expId: string) => Promise<boolean>;
  getExperience: (expId: string) => MasterExperience | undefined;

  // AI优化管理
  optimizeExperience: (expId: string, targetFocus: string) => Promise<boolean>;
  getOptimizedContent: (expId: string, targetFocus: string) => OptimizedContent | undefined;
  updateOptimizedContent: (optId: string, updates: Partial<OptimizedContent>) => Promise<boolean>;

  // 版本管理
  createVersion: (version: Omit<ResumeVersion, 'version_id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<boolean>;
  updateVersion: (versionId: string, updates: Partial<ResumeVersion>) => Promise<boolean>;
  deleteVersion: (versionId: string) => Promise<boolean>;
  getVersion: (versionId: string) => ResumeVersion | undefined;
  duplicateVersion: (versionId: string, newName: string) => Promise<boolean>;

  // 数据加载
  loadUserData: () => Promise<void>;

  // 工具方法
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useResumeStore = create<ResumeState>((set, get) => ({
  // 初始状态
  experiences: [],
  optimizedContent: [],
  versions: [],
  isLoading: false,
  error: null,

  // 添加经历
  addExperience: async (experience) => {
    set({ isLoading: true, error: null });
    
    try {
      const { user } = useAuthStore.getState();
      if (!user) {
        set({ error: '用户未登录', isLoading: false });
        return false;
      }

      const newExperience = await createExperience({
        ...experience,
        user_id: user.user_id,
        raw_facts: experience.raw_facts as unknown as Json
      });

      if (newExperience) {
        // 转换数据库类型到前端类型
        const frontendExperience: MasterExperience = {
          ...newExperience,
          end_date: newExperience.end_date || undefined,
          raw_facts: newExperience.raw_facts as unknown as RawFacts
        };

        set(state => ({
          experiences: [...state.experiences, frontendExperience],
          isLoading: false
        }));
        return true;
      }

      set({ error: '添加经历失败', isLoading: false });
      return false;
    } catch {
      set({
        error: '添加经历失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 更新经历
  updateExperience: async (expId, updates) => {
    set({ isLoading: true, error: null });

    try {
      // 转换前端类型到数据库类型
      const dbUpdates = {
        ...updates,
        raw_facts: updates.raw_facts ? updates.raw_facts as unknown as Json : undefined
      };
      const updatedExperience = await updateExperience(expId, dbUpdates);
      
      if (updatedExperience) {
        // 转换数据库类型到前端类型
        const frontendExperience: MasterExperience = {
          ...updatedExperience,
          end_date: updatedExperience.end_date || undefined,
          raw_facts: updatedExperience.raw_facts as unknown as RawFacts
        };

        set(state => ({
          experiences: state.experiences.map(exp =>
            exp.exp_id === expId ? frontendExperience : exp
          ),
          isLoading: false
        }));
        return true;
      }

      set({ error: '更新经历失败', isLoading: false });
      return false;
    } catch {
      set({
        error: '更新经历失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 删除经历
  deleteExperience: async (expId) => {
    set({ isLoading: true, error: null });
    
    try {
      const success = await deleteExperience(expId);
      
      if (success) {
        set(state => ({
          experiences: state.experiences.filter(exp => exp.exp_id !== expId),
          optimizedContent: state.optimizedContent.filter(opt => opt.exp_id !== expId),
          isLoading: false
        }));
        return true;
      }

      set({ error: '删除经历失败', isLoading: false });
      return false;
    } catch {
      set({
        error: '删除经历失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 获取经历
  getExperience: (expId) => {
    return get().experiences.find(exp => exp.exp_id === expId);
  },

  // AI优化经历
  optimizeExperience: async (expId, targetFocus) => {
    set({ isLoading: true, error: null });
    
    try {
      // TODO: 调用AI优化API
      const newOptimizedContent: OptimizedContent = {
        opt_id: crypto.randomUUID(),
        exp_id: expId,
        target_focus: targetFocus,
        optimized_bullets: ['优化后的项目描述...'], // 从API返回
        ai_model: 'gpt-4',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      set(state => ({
        optimizedContent: [...state.optimizedContent, newOptimizedContent],
        isLoading: false
      }));

      return true;
    } catch {
      set({
        error: 'AI优化失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 获取优化内容
  getOptimizedContent: (expId, targetFocus) => {
    return get().optimizedContent.find(opt => 
      opt.exp_id === expId && opt.target_focus === targetFocus
    );
  },

  // 更新优化内容
  updateOptimizedContent: async (optId, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      // TODO: 调用API更新优化内容
      set(state => ({
        optimizedContent: state.optimizedContent.map(opt =>
          opt.opt_id === optId
            ? { ...opt, ...updates, updated_at: new Date().toISOString() }
            : opt
        ),
        isLoading: false
      }));

      return true;
    } catch {
      set({
        error: '更新优化内容失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 创建版本
  createVersion: async (version) => {
    set({ isLoading: true, error: null });
    
    try {
      // TODO: 调用API创建版本
      const newVersion: ResumeVersion = {
        ...version,
        version_id: crypto.randomUUID(),
        user_id: '', // 从auth store获取
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      set(state => ({
        versions: [...state.versions, newVersion],
        isLoading: false
      }));

      return true;
    } catch {
      set({
        error: '创建版本失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 更新版本
  updateVersion: async (versionId, updates) => {
    set({ isLoading: true, error: null });
    
    try {
      // TODO: 调用API更新版本
      set(state => ({
        versions: state.versions.map(version =>
          version.version_id === versionId
            ? { ...version, ...updates, updated_at: new Date().toISOString() }
            : version
        ),
        isLoading: false
      }));

      return true;
    } catch {
      set({
        error: '更新版本失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 删除版本
  deleteVersion: async (versionId) => {
    set({ isLoading: true, error: null });
    
    try {
      // TODO: 调用API删除版本
      set(state => ({
        versions: state.versions.filter(version => version.version_id !== versionId),
        isLoading: false
      }));

      return true;
    } catch {
      set({
        error: '删除版本失败，请稍后重试',
        isLoading: false
      });
      return false;
    }
  },

  // 获取版本
  getVersion: (versionId) => {
    return get().versions.find(version => version.version_id === versionId);
  },

  // 复制版本
  duplicateVersion: async (versionId, newName) => {
    const originalVersion = get().getVersion(versionId);
    if (!originalVersion) {
      set({ error: '原版本不存在' });
      return false;
    }

    const duplicatedVersion = {
      version_name: newName,
      template_id: originalVersion.template_id,
      version_config: originalVersion.version_config
    };

    return get().createVersion(duplicatedVersion);
  },

  // 清除错误
  clearError: () => {
    set({ error: null });
  },

  // 加载用户数据
  loadUserData: async () => {
    set({ isLoading: true, error: null });
    
    try {
      const { user } = useAuthStore.getState();
      if (!user) {
        set({ error: '用户未登录', isLoading: false });
        return;
      }

      // 并行加载所有数据
      const [dbExperiences, dbVersions] = await Promise.all([
        getUserExperiences(user.user_id),
        getUserResumeVersions(user.user_id)
      ]);

      // 转换数据库类型到前端类型
      const experiences: MasterExperience[] = dbExperiences.map(exp => ({
        ...exp,
        end_date: exp.end_date || undefined,
        raw_facts: exp.raw_facts as unknown as RawFacts
      }));

      const versions: ResumeVersion[] = dbVersions.map(ver => ({
        ...ver,
        version_config: ver.version_config as unknown as VersionConfig
      }));

      // 加载所有经历的优化内容
      const optimizedContentPromises = experiences.map(exp =>
        getOptimizedContent(exp.exp_id)
      );
      const dbOptimizedContent = await Promise.all(optimizedContentPromises);

      // 转换优化内容类型
      const optimizedContent: OptimizedContent[] = dbOptimizedContent.flat().map(content => ({
        ...content,
        optimized_bullets: content.optimized_bullets as unknown as string[]
      }));

      set({
        experiences,
        optimizedContent,
        versions,
        isLoading: false
      });
    } catch {
      set({
        error: '加载数据失败，请稍后重试',
        isLoading: false
      });
    }
  },

  // 设置加载状态
  setLoading: (loading) => {
    set({ isLoading: loading });
  }
}));
