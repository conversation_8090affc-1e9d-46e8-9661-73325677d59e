/**
 * 用户认证状态管理
 * 
 * 使用Zustand管理用户认证状态，包括登录、注册、登出等功能
 * 状态持久化到localStorage，确保页面刷新后状态不丢失
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User } from '@/types';
import { getCurrentUser, signIn, signUp, signOut } from '@/lib/supabase';
import { getUserProfile, createUserProfile } from '@/lib/database';

interface AuthState {
  // 状态
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;

  // 操作
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, fullName?: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User | null) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      // 初始状态
      user: null,
      isLoading: false,
      isAuthenticated: false,
      error: null,

      // 登录操作
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const { data, error } = await signIn(email, password);
          
          if (error) {
            set({ 
              error: error.message,
              isLoading: false,
              isAuthenticated: false,
              user: null
            });
            return false;
          }

          if (data.user) {
            try {
              // 获取或创建用户资料
              let userProfile = await getUserProfile(data.user.id);

              // 如果用户资料不存在，创建默认资料
              if (!userProfile) {
                try {
                  userProfile = await createUserProfile({
                    user_id: data.user.id,
                    full_name: data.user.user_metadata?.full_name || undefined,
                    title: undefined,
                    contact_info: {}
                  });
                } catch (createError) {
                  console.warn('创建用户资料失败，使用默认数据:', createError);
                  // 即使创建失败，也允许用户登录，使用默认数据
                  userProfile = null;
                }
              }

              const user: User = {
                user_id: data.user.id,
                email: data.user.email!,
                full_name: userProfile?.full_name || undefined,
                title: userProfile?.title || undefined,
                contact_info: (userProfile?.contact_info as Record<string, unknown>) || {},
                subscription_status: 'trial',
                created_at: data.user.created_at,
                updated_at: data.user.updated_at || data.user.created_at
              };

              set({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });
              return true;
            } catch (profileError) {
              console.error('获取/创建用户资料失败:', profileError);
              // 即使用户资料操作失败，也允许用户登录
              const user: User = {
                user_id: data.user.id,
                email: data.user.email!,
                full_name: data.user.user_metadata?.full_name || undefined,
                title: undefined,
                contact_info: {},
                subscription_status: 'trial',
                created_at: data.user.created_at,
                updated_at: data.user.updated_at || data.user.created_at
              };

              set({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });
              return true;
            }
          }

          return false;
        } catch (error) {
          console.error('登录异常:', error);
          set({
            error: '登录失败，请稍后重试',
            isLoading: false,
            isAuthenticated: false,
            user: null
          });
          return false;
        }
      },

      // 注册操作
      register: async (email: string, password: string, fullName?: string) => {
        set({ isLoading: true, error: null });

        try {
          const { data, error } = await signUp(email, password);

          if (error) {
            set({
              error: error.message,
              isLoading: false,
              isAuthenticated: false,
              user: null
            });
            return false;
          }

          if (data.user) {
            try {
              // 为新用户创建默认资料
              let userProfile;
              try {
                userProfile = await createUserProfile({
                  user_id: data.user.id,
                  full_name: fullName || data.user.user_metadata?.full_name || undefined,
                  title: undefined,
                  contact_info: {}
                });
              } catch (createError) {
                console.warn('创建用户资料失败，使用默认数据:', createError);
                userProfile = null;
              }

              const user: User = {
                user_id: data.user.id,
                email: data.user.email!,
                full_name: userProfile?.full_name || undefined,
                title: userProfile?.title || undefined,
                contact_info: (userProfile?.contact_info as Record<string, unknown>) || {},
                subscription_status: 'trial',
                created_at: data.user.created_at,
                updated_at: data.user.updated_at || data.user.created_at
              };

              set({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });
              return true;
            } catch (profileError) {
              console.error('注册流程异常:', profileError);
              // 即使用户资料操作失败，也允许用户注册成功
              const user: User = {
                user_id: data.user.id,
                email: data.user.email!,
                full_name: data.user.user_metadata?.full_name || undefined,
                title: undefined,
                contact_info: {},
                subscription_status: 'trial',
                created_at: data.user.created_at,
                updated_at: data.user.updated_at || data.user.created_at
              };

              set({
                user,
                isAuthenticated: true,
                isLoading: false,
                error: null
              });
              return true;
            }
          }

          return false;
        } catch (error) {
          console.error('注册异常:', error);
          set({
            error: '注册失败，请稍后重试',
            isLoading: false,
            isAuthenticated: false,
            user: null
          });
          return false;
        }
      },

      // 登出操作
      logout: async () => {
        set({ isLoading: true });
        
        try {
          await signOut();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        } catch (error) {
          console.error('登出异常:', error);
          set({
            error: '登出失败，请稍后重试',
            isLoading: false
          });
        }
      },

      // 检查认证状态
      checkAuth: async () => {
        set({ isLoading: true });
        
        try {
          const user = await getCurrentUser();
          
          if (user) {
            // 获取用户资料
            const userProfile = await getUserProfile(user.id);
            
            const userData: User = {
              user_id: user.id,
              email: user.email!,
              full_name: userProfile?.full_name || undefined,
              title: userProfile?.title || undefined,
              contact_info: (userProfile?.contact_info as Record<string, unknown>) || {},
              subscription_status: 'trial',
              created_at: user.created_at,
              updated_at: user.updated_at || user.created_at
            };

            set({
              user: userData,
              isAuthenticated: true,
              isLoading: false,
              error: null
            });
          } else {
            set({
              user: null,
              isAuthenticated: false,
              isLoading: false,
              error: null
            });
          }
        } catch (error) {
          console.error('认证检查异常:', error);
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: '认证检查失败'
          });
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 设置用户信息
      setUser: (user: User | null) => {
        set({ 
          user,
          isAuthenticated: !!user
        });
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
);
