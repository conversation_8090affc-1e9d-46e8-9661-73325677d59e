/**
 * 表单验证工具函数
 * 
 * 提供常用的表单验证逻辑，包括邮箱、密码、必填项等验证
 */

/**
 * 验证邮箱格式
 * @param email - 要验证的邮箱地址
 * @returns 是否为有效邮箱格式
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证密码强度
 * @param password - 要验证的密码
 * @returns 密码强度信息
 */
export function validatePassword(password: string): {
  isValid: boolean;
  strength: 'weak' | 'medium' | 'strong';
  errors: string[];
} {
  const errors: string[] = [];
  let strength: 'weak' | 'medium' | 'strong' = 'weak';

  if (password.length < 8) {
    errors.push('密码长度至少8位');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含至少一个大写字母');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含至少一个小写字母');
  }

  if (!/\d/.test(password)) {
    errors.push('密码必须包含至少一个数字');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含至少一个特殊字符');
  }

  // 计算密码强度
  if (errors.length === 0) {
    strength = 'strong';
  } else if (errors.length <= 2) {
    strength = 'medium';
  }

  return {
    isValid: errors.length === 0,
    strength,
    errors
  };
}

/**
 * 验证必填字段
 * @param value - 要验证的值
 * @param fieldName - 字段名称
 * @returns 验证结果
 */
export function validateRequired(value: unknown, fieldName: string): {
  isValid: boolean;
  message?: string;
} {
  if (value === null || value === undefined || value === '') {
    return {
      isValid: false,
      message: `${fieldName}是必填项`
    };
  }

  if (typeof value === 'string' && value.trim() === '') {
    return {
      isValid: false,
      message: `${fieldName}不能为空`
    };
  }

  return { isValid: true };
}

/**
 * 验证日期范围
 * @param startDate - 开始日期
 * @param endDate - 结束日期
 * @returns 验证结果
 */
export function validateDateRange(startDate: string, endDate?: string): {
  isValid: boolean;
  message?: string;
} {
  const start = new Date(startDate);
  const now = new Date();

  if (start > now) {
    return {
      isValid: false,
      message: '开始日期不能晚于今天'
    };
  }

  if (endDate) {
    const end = new Date(endDate);
    if (end < start) {
      return {
        isValid: false,
        message: '结束日期不能早于开始日期'
      };
    }
  }

  return { isValid: true };
}

/**
 * 验证技术栈标签
 * @param technologies - 技术栈数组
 * @returns 验证结果
 */
export function validateTechnologies(technologies: string[]): {
  isValid: boolean;
  message?: string;
} {
  if (technologies.length === 0) {
    return {
      isValid: false,
      message: '请至少选择一个技术栈'
    };
  }

  if (technologies.length > 20) {
    return {
      isValid: false,
      message: '技术栈数量不能超过20个'
    };
  }

  // 检查是否有重复
  const uniqueTechnologies = new Set(technologies);
  if (uniqueTechnologies.size !== technologies.length) {
    return {
      isValid: false,
      message: '技术栈不能重复'
    };
  }

  return { isValid: true };
}
