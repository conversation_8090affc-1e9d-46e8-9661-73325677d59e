/**
 * 添加新经历页面
 * 
 * 用户可以在此页面添加新的工作经历或项目经历
 * 包含结构化的表单和Raw Facts录入
 */

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useResumeStore } from '@/store/resume';
import { AuthGuard } from '@/components/auth/auth-guard';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading';
import { validateDateRange, validateTechnologies } from '@/utils/validation';
import { RawFacts } from '@/types';

/**
 * 添加经历页面组件
 */
export default function NewExperiencePage() {
  const router = useRouter();
  const { addExperience, isLoading, error } = useResumeStore();

  const [formData, setFormData] = useState({
    company_name: '',
    role_title: '',
    start_date: '',
    end_date: '',
    is_current: false,
    raw_facts: {
      description: '',
      technologies: [] as string[],
      achievements: [] as string[],
      responsibilities: [] as string[],
      quantified_data: ''
    } as RawFacts
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [techInput, setTechInput] = useState('');
  const [achievementInput, setAchievementInput] = useState('');
  const [responsibilityInput, setResponsibilityInput] = useState('');


  // 处理基本字段变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除对应字段的错误
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  // 添加技术栈
  const addTechnology = () => {
    if (techInput.trim() && !formData.raw_facts.technologies.includes(techInput.trim())) {
      setFormData(prev => ({
        ...prev,
        raw_facts: {
          ...prev.raw_facts,
          technologies: [...prev.raw_facts.technologies, techInput.trim()]
        }
      }));
      setTechInput('');
    }
  };

  // 移除技术栈
  const removeTechnology = (index: number) => {
    setFormData(prev => ({
      ...prev,
      raw_facts: {
        ...prev.raw_facts,
        technologies: prev.raw_facts.technologies.filter((_, i) => i !== index)
      }
    }));
  };

  // 添加成就
  const addAchievement = () => {
    if (achievementInput.trim()) {
      setFormData(prev => ({
        ...prev,
        raw_facts: {
          ...prev.raw_facts,
          achievements: [...prev.raw_facts.achievements, achievementInput.trim()]
        }
      }));
      setAchievementInput('');
    }
  };

  // 移除成就
  const removeAchievement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      raw_facts: {
        ...prev.raw_facts,
        achievements: prev.raw_facts.achievements.filter((_, i) => i !== index)
      }
    }));
  };

  // 添加职责
  const addResponsibility = () => {
    if (responsibilityInput.trim()) {
      setFormData(prev => ({
        ...prev,
        raw_facts: {
          ...prev.raw_facts,
          responsibilities: [...prev.raw_facts.responsibilities, responsibilityInput.trim()]
        }
      }));
      setResponsibilityInput('');
    }
  };

  // 移除职责
  const removeResponsibility = (index: number) => {
    setFormData(prev => ({
      ...prev,
      raw_facts: {
        ...prev.raw_facts,
        responsibilities: prev.raw_facts.responsibilities.filter((_, i) => i !== index)
      }
    }));
  };



  // 验证表单
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.company_name.trim()) {
      errors.company_name = "公司/组织名称是必填项";
    }

    if (!formData.role_title.trim()) {
      errors.role_title = "职位/项目名称是必填项";
    }

    if (!formData.start_date) {
      errors.start_date = "开始日期是必填项";
    }

    // 验证日期范围
    if (formData.start_date && !formData.is_current) {
      const dateValidation = validateDateRange(formData.start_date, formData.end_date || undefined);
      if (!dateValidation.isValid) {
        errors.start_date = dateValidation.message!;
      }
    }

    // 验证技术栈
    if (formData.raw_facts.technologies.length > 0) {
      const techValidation = validateTechnologies(formData.raw_facts.technologies);
      if (!techValidation.isValid) {
        errors.technologies = techValidation.message!;
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !user) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // 构建经历数据
      const experienceData = {
        user_id: user.user_id,
        company_name: formData.company_name,
        role_title: formData.role_title,
        start_date: formData.start_date,
        end_date: formData.is_current ? undefined : (formData.end_date || undefined),
        is_current: formData.is_current,
        raw_facts: formData.raw_facts
      };

      const success = await addExperience(experienceData);

      if (success) {
        setSuccess('经历添加成功！');
        // 重置表单
        setFormData({
          company_name: '',
          role_title: '',
          start_date: '',
          end_date: '',
          is_current: false,
          raw_facts: {
            description: '',
            technologies: [],
            achievements: [],
            responsibilities: [],
            quantified_data: ''
          }
        });

        // 3秒后跳转到经历列表
        setTimeout(() => {
          router.push('/experiences');
        }, 3000);
      } else {
        setError('添加失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('添加经历失败:', error);
      setError(error.message || '添加失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthGuard>
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 页面头部 */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                添加新经历
              </h1>
              <p className="text-gray-600">
                录入您的工作经历或项目经历，建立简历数据源
              </p>
            </div>

            {/* 表单 */}
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* 基本信息 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">基本信息</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    name="company_name"
                    label="公司/组织名称"
                    value={formData.company_name}
                    onChange={handleInputChange}
                    error={formErrors.company_name}
                    placeholder="请输入公司或组织名称"
                    required
                  />

                  <Input
                    name="role_title"
                    label="职位/项目名称"
                    value={formData.role_title}
                    onChange={handleInputChange}
                    error={formErrors.role_title}
                    placeholder="请输入职位或项目名称"
                    required
                  />

                  <Input
                    name="location"
                    label="工作地点"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="请输入工作地点（可选）"
                  />

                  <Input
                    name="start_date"
                    label="开始日期"
                    type="date"
                    value={formData.start_date}
                    onChange={handleInputChange}
                    error={formErrors.start_date}
                    required
                  />

                  <div>
                    <label className="flex items-center space-x-2 mb-4">
                      <input
                        type="checkbox"
                        name="is_current"
                        checked={formData.is_current}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          is_current: e.target.checked,
                          end_date: e.target.checked ? '' : prev.end_date
                        }))}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm font-medium text-gray-700">
                        这是我的当前工作/项目
                      </span>
                    </label>

                    {!formData.is_current && (
                      <Input
                        name="end_date"
                        label="结束日期"
                        type="date"
                        value={formData.end_date}
                        onChange={handleInputChange}
                        placeholder="请选择结束日期"
                      />
                    )}
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    经历描述
                  </label>
                  <textarea
                    name="description"
                    value={formData.raw_facts.description}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      raw_facts: {
                        ...prev.raw_facts,
                        description: e.target.value
                      }
                    }))}
                    rows={3}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="简要描述这段经历的背景和概况"
                  />
                </div>
              </div>

              {/* 技术栈 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">技术栈</h2>

                <div className="flex space-x-2 mb-4">
                  <input
                    type="text"
                    value={techInput}
                    onChange={(e) => setTechInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTechnology())}
                    className="flex-1 rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入技术栈，按回车添加"
                  />
                  <Button type="button" onClick={addTechnology}>
                    添加
                  </Button>
                </div>

                {formErrors.technologies && (
                  <p className="text-sm text-red-600 mb-4">{formErrors.technologies}</p>
                )}

                <div className="flex flex-wrap gap-2">
                  {formData.raw_facts.technologies.map((tech, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
                    >
                      {tech}
                      <button
                        type="button"
                        onClick={() => removeTechnology(index)}
                        className="ml-2 text-blue-600 hover:text-blue-800"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>

              {/* Raw Facts - 原始事实录入 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Raw Facts - 原始事实</h2>
                <p className="text-gray-600 mb-6">
                  录入原始的工作事实，这些数据将用于AI优化生成不同版本的简历内容
                </p>

                {/* 成就 */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">主要成就</h3>
                  <div className="flex space-x-2 mb-3">
                    <input
                      type="text"
                      value={achievementInput}
                      onChange={(e) => setAchievementInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAchievement())}
                      className="flex-1 rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="描述一个具体的成就或项目成果"
                    />
                    <Button type="button" onClick={addAchievement}>
                      添加
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {formData.raw_facts.achievements.map((achievement, index) => (
                      <div key={index} className="flex items-start space-x-2 p-3 bg-green-50 rounded-md">
                        <span className="flex-1 text-sm">{achievement}</span>
                        <button
                          type="button"
                          onClick={() => removeAchievement(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          删除
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 职责 */}
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">主要职责</h3>
                  <div className="flex space-x-2 mb-3">
                    <input
                      type="text"
                      value={responsibilityInput}
                      onChange={(e) => setResponsibilityInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addResponsibility())}
                      className="flex-1 rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="描述一个具体的工作职责"
                    />
                    <Button type="button" onClick={addResponsibility}>
                      添加
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {formData.raw_facts.responsibilities.map((responsibility, index) => (
                      <div key={index} className="flex items-start space-x-2 p-3 bg-blue-50 rounded-md">
                        <span className="flex-1 text-sm">{responsibility}</span>
                        <button
                          type="button"
                          onClick={() => removeResponsibility(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          删除
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 量化数据 */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">量化数据</h3>
                  <textarea
                    name="quantified_data"
                    value={formData.raw_facts.quantified_data || ''}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      raw_facts: {
                        ...prev.raw_facts,
                        quantified_data: e.target.value
                      }
                    }))}
                    rows={3}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入具体的数据指标和量化成果，如：提升系统性能30%，减少响应时间从3秒到1秒，管理团队15人等"
                  />
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      保存中...
                    </div>
                  ) : (
                    '保存经历'
                  )}
                </Button>
              </div>
            </form>

            {/* 全局错误提示 */}
            {error && (
              <div className="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
