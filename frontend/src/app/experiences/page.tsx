/**
 * 经历管理页面
 * 
 * 显示用户的所有工作经历，支持添加、编辑、删除操作
 * 这是简历数据的核心管理界面
 */

'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useResumeStore } from '@/store/resume';
import { AuthGuard } from '@/components/auth/auth-guard';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading';
import { MasterExperience } from '@/types';

/**
 * 经历管理页面组件
 */
export default function ExperiencesPage() {
  const {
    experiences,
    isLoading,
    error,
    loadUserData,
    deleteExperience
  } = useResumeStore();

  const [deletingId, setDeletingId] = useState<string | null>(null);

  // 加载用户数据
  useEffect(() => {
    loadUserData();
  }, [loadUserData]);

  // 处理删除经历
  const handleDelete = async (expId: string) => {
    if (!confirm('确定要删除这个经历吗？此操作不可撤销。')) {
      return;
    }

    setDeletingId(expId);
    try {
      const success = await deleteExperience(expId);
      if (success) {
        // 删除成功，数据会自动更新
      }
    } finally {
      setDeletingId(null);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long'
    });
  };

  // 格式化日期范围
  const formatDateRange = (startDate: string, endDate?: string) => {
    const start = formatDate(startDate);
    const end = endDate ? formatDate(endDate) : '至今';
    return `${start} - ${end}`;
  };

  if (isLoading) {
    return (
      <AuthGuard>
        <MainLayout>
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-64">
              <LoadingSpinner size="lg" />
            </div>
          </div>
        </MainLayout>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            {/* 页面头部 */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  工作经历管理
                </h1>
                <p className="text-gray-600">
                  管理您的工作经历，这些数据将用于生成不同版本的简历
                </p>
              </div>
              <Link href="/experiences/new">
                <Button>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  添加经历
                </Button>
              </Link>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* 经历列表 */}
            {experiences.length === 0 ? (
              <div className="text-center py-12">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  还没有工作经历
                </h3>
                <p className="text-gray-500 mb-6">
                  开始添加您的工作经历，建立简历数据源
                </p>
                <Link href="/experiences/new">
                  <Button>
                    添加第一个经历
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-6">
                {experiences.map((experience) => (
                  <div
                    key={experience.exp_id}
                    className="bg-white rounded-lg shadow border border-gray-200 p-6"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-semibold text-gray-900">
                            {experience.position}
                          </h3>
                          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full">
                            {experience.experience_type}
                          </span>
                        </div>
                        
                        <div className="text-gray-600 mb-2">
                          <span className="font-medium">{experience.company}</span>
                          {experience.location && (
                            <span className="ml-2">• {experience.location}</span>
                          )}
                        </div>
                        
                        <div className="text-sm text-gray-500 mb-4">
                          {formatDateRange(experience.start_date, experience.end_date)}
                        </div>

                        {/* 技术栈 */}
                        {experience.technologies && experience.technologies.length > 0 && (
                          <div className="mb-4">
                            <div className="flex flex-wrap gap-2">
                              {experience.technologies.map((tech, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded"
                                >
                                  {tech}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* 描述 */}
                        {experience.description && (
                          <p className="text-gray-700 text-sm mb-4">
                            {experience.description}
                          </p>
                        )}

                        {/* Raw Facts 数量 */}
                        <div className="text-xs text-gray-500">
                          {experience.raw_facts?.achievements?.length || 0} 个成就记录
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center space-x-2 ml-4">
                        <Link href={`/experiences/${experience.exp_id}/edit`}>
                          <Button variant="outline" size="sm">
                            编辑
                          </Button>
                        </Link>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(experience.exp_id)}
                          disabled={deletingId === experience.exp_id}
                        >
                          {deletingId === experience.exp_id ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            '删除'
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 统计信息 */}
            {experiences.length > 0 && (
              <div className="mt-8 bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">统计信息</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {experiences.length}
                    </div>
                    <div className="text-sm text-gray-600">总经历数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {experiences.filter(exp => exp.experience_type === 'work').length}
                    </div>
                    <div className="text-sm text-gray-600">工作经历</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {experiences.filter(exp => exp.experience_type === 'project').length}
                    </div>
                    <div className="text-sm text-gray-600">项目经历</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
