/**
 * 简历版本管理页面
 * 
 * 显示用户的所有简历版本，支持创建、编辑、删除、复制操作
 * 实现"一改全改"机制的核心界面
 */

'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useResumeStore } from '@/store/resume';
import { AuthGuard } from '@/components/auth/auth-guard';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading';
import { ResumeVersion } from '@/types';

/**
 * 简历版本管理页面组件
 */
export default function VersionsPage() {
  const {
    versions,
    isLoading,
    error,
    loadUserData,
    deleteVersion,
    duplicateVersion
  } = useResumeStore();

  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [duplicatingId, setDuplicatingId] = useState<string | null>(null);

  // 加载用户数据
  useEffect(() => {
    loadUserData();
  }, [loadUserData]);

  // 处理删除版本
  const handleDelete = async (versionId: string) => {
    if (!confirm('确定要删除这个简历版本吗？此操作不可撤销。')) {
      return;
    }

    setDeletingId(versionId);
    try {
      const success = await deleteVersion(versionId);
      if (success) {
        // 删除成功，数据会自动更新
      }
    } finally {
      setDeletingId(null);
    }
  };

  // 处理复制版本
  const handleDuplicate = async (versionId: string, currentName: string) => {
    const newName = prompt('请输入新版本的名称:', `${currentName} - 副本`);
    if (!newName || !newName.trim()) {
      return;
    }

    setDuplicatingId(versionId);
    try {
      const success = await duplicateVersion(versionId, newName.trim());
      if (success) {
        // 复制成功，数据会自动更新
      }
    } finally {
      setDuplicatingId(null);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <AuthGuard>
        <MainLayout>
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-64">
              <LoadingSpinner size="lg" />
            </div>
          </div>
        </MainLayout>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            {/* 页面头部 */}
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">
                  简历版本管理
                </h1>
                <p className="text-gray-600">
                  管理您的简历版本，针对不同岗位创建定制化简历
                </p>
              </div>
              <Link href="/versions/new">
                <Button>
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  创建版本
                </Button>
              </Link>
            </div>

            {/* 错误提示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* 版本列表 */}
            {versions.length === 0 ? (
              <div className="text-center py-12">
                <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  还没有简历版本
                </h3>
                <p className="text-gray-500 mb-6">
                  创建您的第一个简历版本，开始针对不同岗位定制简历
                </p>
                <Link href="/versions/new">
                  <Button>
                    创建第一个版本
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {versions.map((version) => (
                  <div
                    key={version.version_id}
                    className="bg-white rounded-lg shadow border border-gray-200 p-6 hover:shadow-lg transition-shadow"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {version.version_name}
                        </h3>
                        
                        {version.target_position && (
                          <p className="text-sm text-gray-600 mb-2">
                            目标职位: {version.target_position}
                          </p>
                        )}
                        
                        {version.target_company && (
                          <p className="text-sm text-gray-600 mb-2">
                            目标公司: {version.target_company}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          version.status === 'active' 
                            ? 'bg-green-100 text-green-800'
                            : version.status === 'draft'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {version.status === 'active' ? '活跃' : 
                           version.status === 'draft' ? '草稿' : '已归档'}
                        </span>
                      </div>
                    </div>

                    {/* 版本配置信息 */}
                    <div className="mb-4 text-sm text-gray-600">
                      <p>包含经历: {version.version_config?.included_experiences?.length || 0} 个</p>
                      <p>更新时间: {formatDate(version.updated_at)}</p>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex flex-wrap gap-2">
                      <Link href={`/versions/${version.version_id}/edit`}>
                        <Button variant="outline" size="sm">
                          编辑
                        </Button>
                      </Link>
                      
                      <Link href={`/versions/${version.version_id}/preview`}>
                        <Button variant="outline" size="sm">
                          预览
                        </Button>
                      </Link>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDuplicate(version.version_id, version.version_name)}
                        disabled={duplicatingId === version.version_id}
                      >
                        {duplicatingId === version.version_id ? (
                          <LoadingSpinner size="sm" />
                        ) : (
                          '复制'
                        )}
                      </Button>
                      
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(version.version_id)}
                        disabled={deletingId === version.version_id}
                      >
                        {deletingId === version.version_id ? (
                          <LoadingSpinner size="sm" />
                        ) : (
                          '删除'
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 统计信息 */}
            {versions.length > 0 && (
              <div className="mt-8 bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">统计信息</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {versions.length}
                    </div>
                    <div className="text-sm text-gray-600">总版本数</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {versions.filter(v => v.status === 'active').length}
                    </div>
                    <div className="text-sm text-gray-600">活跃版本</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">
                      {versions.filter(v => v.status === 'draft').length}
                    </div>
                    <div className="text-sm text-gray-600">草稿版本</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">
                      {versions.filter(v => v.status === 'archived').length}
                    </div>
                    <div className="text-sm text-gray-600">已归档</div>
                  </div>
                </div>
              </div>
            )}

            {/* 使用提示 */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                💡 使用提示
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 为不同的目标职位创建专门的简历版本</li>
                <li>• 修改经历数据时，所有版本会自动更新（一改全改）</li>
                <li>• 使用复制功能快速创建相似版本</li>
                <li>• 定期预览和导出PDF确保格式正确</li>
              </ul>
            </div>
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
