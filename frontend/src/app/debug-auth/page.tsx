/**
 * 认证调试页面
 * 
 * 用于调试认证功能的专用页面
 */

"use client";

import { useState, useEffect } from "react";
import { useAuthStore } from "@/store/auth";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function DebugAuthPage() {
  const { user, isAuthenticated, isLoading, error, login, register, logout, checkAuth, clearError } = useAuthStore();
  const [testEmail, setTestEmail] = useState(`debug_${Date.now()}@example.com`);
  const [testPassword, setTestPassword] = useState('TestPassword123!');
  const [testName, setTestName] = useState('调试用户');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    addLog('页面加载完成');
    checkAuth();
  }, [checkAuth]);

  const handleRegister = async () => {
    addLog(`开始注册: ${testEmail}`);
    clearError();
    
    const success = await register(testEmail, testPassword, testName);
    
    if (success) {
      addLog('注册成功');
    } else {
      addLog(`注册失败: ${error}`);
    }
  };

  const handleLogin = async () => {
    addLog(`开始登录: ${testEmail}`);
    clearError();
    
    const success = await login(testEmail, testPassword);
    
    if (success) {
      addLog('登录成功');
    } else {
      addLog(`登录失败: ${error}`);
    }
  };

  const handleLogout = async () => {
    addLog('开始登出');
    await logout();
    addLog('登出完成');
  };

  const testSupabaseConnection = async () => {
    addLog('测试Supabase连接...');
    
    try {
      const { data, error } = await supabase.from('user_profiles').select('count').limit(1);
      
      if (error) {
        addLog(`连接失败: ${error.message}`);
      } else {
        addLog('连接成功');
      }
    } catch (error) {
      addLog(`连接异常: ${error.message}`);
    }
  };

  const checkSession = async () => {
    addLog('检查会话状态...');
    
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        addLog(`获取会话失败: ${error.message}`);
      } else if (session) {
        addLog('会话已建立');
        addLog(`用户ID: ${session.user.id}`);
        addLog(`邮箱: ${session.user.email}`);
      } else {
        addLog('当前无活跃会话');
      }
    } catch (error) {
      addLog(`检查会话异常: ${error.message}`);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">🔧 认证功能调试</h1>
      
      {/* 当前状态 */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-2">当前状态</h2>
        <p>认证状态: {isAuthenticated ? '✅ 已登录' : '❌ 未登录'}</p>
        <p>加载状态: {isLoading ? '🔄 加载中' : '✅ 空闲'}</p>
        <p>用户信息: {user ? `${user.email} (${user.user_id})` : '无'}</p>
        {error && <p className="text-red-600">错误: {error}</p>}
      </div>

      {/* 测试表单 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">测试表单</h2>
          
          <Input
            label="测试邮箱"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            placeholder="测试邮箱"
          />
          
          <Input
            label="测试密码"
            type="password"
            value={testPassword}
            onChange={(e) => setTestPassword(e.target.value)}
            placeholder="测试密码"
          />
          
          <Input
            label="测试姓名"
            value={testName}
            onChange={(e) => setTestName(e.target.value)}
            placeholder="测试姓名"
          />
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">操作按钮</h2>
          
          <div className="grid grid-cols-2 gap-2">
            <Button onClick={handleRegister} disabled={isLoading}>
              注册
            </Button>
            
            <Button onClick={handleLogin} disabled={isLoading}>
              登录
            </Button>
            
            <Button onClick={handleLogout} disabled={isLoading}>
              登出
            </Button>
            
            <Button onClick={() => checkAuth()} disabled={isLoading}>
              检查认证
            </Button>
            
            <Button onClick={testSupabaseConnection}>
              测试连接
            </Button>
            
            <Button onClick={checkSession}>
              检查会话
            </Button>
          </div>
        </div>
      </div>

      {/* 日志输出 */}
      <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">调试日志</h2>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setLogs([])}
          >
            清除日志
          </Button>
        </div>
        
        <div className="max-h-64 overflow-y-auto">
          {logs.map((log, index) => (
            <div key={index}>{log}</div>
          ))}
          {logs.length === 0 && <div>暂无日志...</div>}
        </div>
      </div>
    </div>
  );
}