/**
 * 根布局组件
 * 
 * 定义整个应用的HTML结构和全局样式
 * 包含元数据、字体、全局样式等配置
 */

import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/components/auth/auth-provider";

// 配置Inter字体
const inter = Inter({ subsets: ["latin"] });

// 应用元数据
export const metadata: Metadata = {
  title: {
    default: "SynapseCV - AI驱动的简历优化平台",
    template: "%s | SynapseCV"
  },
  description: "One Master Profile. Infinite Targeted Resumes. 通过AI智能优化，从单一数据源生成多个定制化简历版本，大幅提升求职效率。",
  keywords: ["简历优化", "AI简历", "求职", "简历模板", "ATS友好", "SDE简历"],
  authors: [{ name: "SynapseCV Team" }],
  creator: "SynapseCV",
  publisher: "SynapseCV",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "/",
    title: "SynapseCV - AI驱动的简历优化平台",
    description: "One Master Profile. Infinite Targeted Resumes. 通过AI智能优化，从单一数据源生成多个定制化简历版本，大幅提升求职效率。",
    siteName: "SynapseCV",
  },
  twitter: {
    card: "summary_large_image",
    title: "SynapseCV - AI驱动的简历优化平台",
    description: "One Master Profile. Infinite Targeted Resumes. 通过AI智能优化，从单一数据源生成多个定制化简历版本，大幅提升求职效率。",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

/**
 * 根布局组件
 * 
 * @param children - 页面内容
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN" className="h-full">
      <body className={`${inter.className} h-full antialiased`}>
        <div id="root" className="h-full">
          <AuthProvider>
            {children}
          </AuthProvider>
        </div>
      </body>
    </html>
  );
}