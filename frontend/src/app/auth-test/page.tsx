/**
 * 认证测试页面
 * 
 * 用于测试认证系统的各种功能
 */

'use client';

import { useState } from 'react';
import { useAuthStore } from '@/store/auth';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { MainLayout } from '@/components/layout/main-layout';

export default function AuthTestPage() {
  const { user, isAuthenticated, isLoading, login, register, logout, checkAuth } = useAuthStore();
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('password123');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testCurrentSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        addResult(`❌ Session检查失败: ${error.message}`);
      } else if (session) {
        addResult(`✅ 当前Session存在: ${session.user?.email}`);
      } else {
        addResult(`ℹ️ 当前无Session`);
      }
    } catch (error) {
      addResult(`❌ Session检查异常: ${error}`);
    }
  };

  const testRegister = async () => {
    try {
      addResult(`🔄 开始注册测试: ${testEmail}`);
      const success = await register(testEmail, testPassword, 'Test User');
      if (success) {
        addResult(`✅ 注册成功`);
      } else {
        addResult(`❌ 注册失败`);
      }
    } catch (error) {
      addResult(`❌ 注册异常: ${error}`);
    }
  };

  const testLogin = async () => {
    try {
      addResult(`🔄 开始登录测试: ${testEmail}`);
      const success = await login(testEmail, testPassword);
      if (success) {
        addResult(`✅ 登录成功`);
      } else {
        addResult(`❌ 登录失败`);
      }
    } catch (error) {
      addResult(`❌ 登录异常: ${error}`);
    }
  };

  const testLogout = async () => {
    try {
      addResult(`🔄 开始登出测试`);
      await logout();
      addResult(`✅ 登出成功`);
    } catch (error) {
      addResult(`❌ 登出异常: ${error}`);
    }
  };

  const testCheckAuth = async () => {
    try {
      addResult(`🔄 开始认证检查测试`);
      await checkAuth();
      addResult(`✅ 认证检查完成`);
    } catch (error) {
      addResult(`❌ 认证检查异常: ${error}`);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">认证系统测试</h1>

          {/* 当前状态 */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">当前认证状态</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 rounded">
                <p className="text-sm text-gray-600">认证状态</p>
                <p className={`font-semibold ${isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                  {isAuthenticated ? '已认证' : '未认证'}
                </p>
              </div>
              <div className="p-4 bg-gray-50 rounded">
                <p className="text-sm text-gray-600">加载状态</p>
                <p className={`font-semibold ${isLoading ? 'text-yellow-600' : 'text-gray-900'}`}>
                  {isLoading ? '加载中' : '空闲'}
                </p>
              </div>
              <div className="p-4 bg-gray-50 rounded">
                <p className="text-sm text-gray-600">用户邮箱</p>
                <p className="font-semibold text-gray-900">
                  {user?.email || '无'}
                </p>
              </div>
            </div>
          </div>

          {/* 测试控制 */}
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">测试控制</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <Input
                label="测试邮箱"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="输入测试邮箱"
              />
              <Input
                label="测试密码"
                type="password"
                value={testPassword}
                onChange={(e) => setTestPassword(e.target.value)}
                placeholder="输入测试密码"
              />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              <Button onClick={testCurrentSession} size="sm">
                检查Session
              </Button>
              <Button onClick={testRegister} size="sm" variant="outline">
                测试注册
              </Button>
              <Button onClick={testLogin} size="sm" variant="outline">
                测试登录
              </Button>
              <Button onClick={testLogout} size="sm" variant="outline">
                测试登出
              </Button>
              <Button onClick={testCheckAuth} size="sm" variant="outline">
                检查认证
              </Button>
            </div>
          </div>

          {/* 测试结果 */}
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">测试结果</h2>
              <Button onClick={clearResults} size="sm" variant="outline">
                清除结果
              </Button>
            </div>
            
            <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-500">暂无测试结果</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 用户信息详情 */}
          {user && (
            <div className="bg-white rounded-lg shadow p-6 mt-6">
              <h2 className="text-xl font-semibold mb-4">用户信息详情</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
