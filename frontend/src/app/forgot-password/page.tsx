/**
 * 忘记密码页面
 * 
 * 用户可以通过邮箱重置密码
 * 发送重置密码邮件到用户邮箱
 */

"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading";
import { isValidEmail } from "@/utils/validation";
import { MainLayout } from "@/components/layout/main-layout";
import { GuestGuard } from "@/components/auth/auth-guard";

/**
 * 忘记密码页面组件
 */
export default function ForgotPasswordPage() {
  const router = useRouter();
  
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError("请输入邮箱地址");
      return;
    }

    if (!isValidEmail(email)) {
      setError("请输入有效的邮箱地址");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      setSuccess(true);
    } catch (error: any) {
      console.error("重置密码失败:", error);
      setError(error.message || "发送重置邮件失败，请稍后重试");
    } finally {
      setIsLoading(false);
    }
  };

  if (success) {
    return (
      <GuestGuard>
        <MainLayout showHeader={false} showFooter={false}>
          <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
              <div className="text-center">
                <div className="flex justify-center">
                  <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <h2 className="mt-6 text-3xl font-bold text-gray-900">
                  邮件已发送
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  我们已向 <strong>{email}</strong> 发送了重置密码的邮件。
                  请检查您的邮箱并点击邮件中的链接来重置密码。
                </p>
                <div className="mt-6 space-y-4">
                  <p className="text-xs text-gray-500">
                    没有收到邮件？请检查垃圾邮件文件夹，或者
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => setSuccess(false)}
                    className="w-full"
                  >
                    重新发送
                  </Button>
                  <Link href="/login">
                    <Button variant="ghost" className="w-full">
                      返回登录
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </MainLayout>
      </GuestGuard>
    );
  }

  return (
    <GuestGuard>
      <MainLayout showHeader={false} showFooter={false}>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            {/* 头部 */}
            <div className="text-center">
              <div className="flex justify-center">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">S</span>
                </div>
              </div>
              <h2 className="mt-6 text-3xl font-bold text-gray-900">
                重置密码
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                输入您的邮箱地址，我们将发送重置密码的链接给您
              </p>
            </div>

            {/* 重置密码表单 */}
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <Input
                id="email"
                name="email"
                type="email"
                label="邮箱地址"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                error={error}
                placeholder="请输入您的邮箱地址"
                required
                autoComplete="email"
                autoFocus
              />

              {/* 提交按钮 */}
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <LoadingSpinner size="sm" className="mr-2" />
                    发送中...
                  </div>
                ) : (
                  "发送重置邮件"
                )}
              </Button>

              {/* 返回登录链接 */}
              <div className="text-center">
                <Link
                  href="/login"
                  className="text-sm text-blue-600 hover:text-blue-500"
                >
                  返回登录
                </Link>
              </div>
            </form>
          </div>
        </div>
      </MainLayout>
    </GuestGuard>
  );
}
