/**
 * 重置密码页面
 * 
 * 用户通过邮件链接访问此页面来设置新密码
 * 验证重置令牌并允许用户设置新密码
 */

"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading";
import { validatePassword } from "@/utils/validation";
import { MainLayout } from "@/components/layout/main-layout";
import { GuestGuard } from "@/components/auth/auth-guard";

/**
 * 重置密码页面组件
 */
export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<{
    isValid: boolean;
    strength: 'weak' | 'medium' | 'strong';
    errors: string[];
  } | null>(null);

  // 检查URL参数中的访问令牌
  useEffect(() => {
    const accessToken = searchParams.get('access_token');
    const refreshToken = searchParams.get('refresh_token');
    
    if (accessToken && refreshToken) {
      // 设置会话
      supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken
      });
    }
  }, [searchParams]);

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除错误
    if (error) {
      setError("");
    }

    // 实时检查密码强度
    if (name === "password" && value) {
      setPasswordStrength(validatePassword(value));
    } else if (name === "password" && !value) {
      setPasswordStrength(null);
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.password) {
      setError("请输入新密码");
      return;
    }

    if (passwordStrength && !passwordStrength.isValid) {
      setError("密码强度不足");
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError("两次输入的密码不一致");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const { error } = await supabase.auth.updateUser({
        password: formData.password
      });

      if (error) {
        throw error;
      }

      setSuccess(true);
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        router.push("/login");
      }, 3000);
    } catch (error: any) {
      console.error("重置密码失败:", error);
      setError(error.message || "重置密码失败，请稍后重试");
    } finally {
      setIsLoading(false);
    }
  };

  // 获取密码强度颜色
  const getPasswordStrengthColor = () => {
    if (!passwordStrength) return "bg-gray-200";
    
    switch (passwordStrength.strength) {
      case "weak":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "strong":
        return "bg-green-500";
      default:
        return "bg-gray-200";
    }
  };

  if (success) {
    return (
      <GuestGuard>
        <MainLayout showHeader={false} showFooter={false}>
          <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
              <div className="text-center">
                <div className="flex justify-center">
                  <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <h2 className="mt-6 text-3xl font-bold text-gray-900">
                  密码重置成功
                </h2>
                <p className="mt-2 text-sm text-gray-600">
                  您的密码已成功重置。正在跳转到登录页面...
                </p>
                <div className="mt-6">
                  <LoadingSpinner size="md" className="mx-auto" />
                </div>
              </div>
            </div>
          </div>
        </MainLayout>
      </GuestGuard>
    );
  }

  return (
    <GuestGuard>
      <MainLayout showHeader={false} showFooter={false}>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8">
            {/* 头部 */}
            <div className="text-center">
              <div className="flex justify-center">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-xl">S</span>
                </div>
              </div>
              <h2 className="mt-6 text-3xl font-bold text-gray-900">
                设置新密码
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                请输入您的新密码
              </p>
            </div>

            {/* 重置密码表单 */}
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              <div className="space-y-4">
                <div>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    label="新密码"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="请输入新密码"
                    required
                    autoComplete="new-password"
                  />
                  
                  {/* 密码强度指示器 */}
                  {passwordStrength && (
                    <div className="mt-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                            style={{
                              width: passwordStrength.strength === "weak" ? "33%" : 
                                     passwordStrength.strength === "medium" ? "66%" : "100%"
                            }}
                          />
                        </div>
                        <span className="text-xs text-gray-600 capitalize">
                          {passwordStrength.strength}
                        </span>
                      </div>
                      
                      {/* 密码要求 */}
                      {passwordStrength.errors.length > 0 && (
                        <ul className="mt-2 text-xs text-gray-600 space-y-1">
                          {passwordStrength.errors.map((error, index) => (
                            <li key={index} className="flex items-center">
                              <svg className="w-3 h-3 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                              </svg>
                              {error}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  )}
                </div>

                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  label="确认新密码"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="请再次输入新密码"
                  required
                  autoComplete="new-password"
                />
              </div>

              {/* 全局错误提示 */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-red-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* 提交按钮 */}
              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <LoadingSpinner size="sm" className="mr-2" />
                    重置中...
                  </div>
                ) : (
                  "重置密码"
                )}
              </Button>
            </form>
          </div>
        </div>
      </MainLayout>
    </GuestGuard>
  );
}
