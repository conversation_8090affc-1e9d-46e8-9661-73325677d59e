/**
 * 数据库测试页面
 * 
 * 用于测试前端与Supabase数据库的集成
 * 仅在开发环境中可用
 */

'use client';

import { useState } from 'react';
import { AuthGuard } from '@/components/auth/auth-guard';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/loading';
import { 
  runAllDatabaseTests, 
  formatTestResults, 
  testDatabaseConnection,
  testAuthentication,
  testUserProfileCRUD,
  testExperienceCRUD,
  TestResult 
} from '@/lib/test-database';

/**
 * 数据库测试页面组件
 */
export default function TestDatabasePage() {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);
  const [output, setOutput] = useState('');

  // 运行所有测试
  const runAllTests = async () => {
    setIsRunning(true);
    setResults([]);
    setOutput('正在运行测试...\n');

    try {
      const testResults = await runAllDatabaseTests();
      setResults(testResults);
      setOutput(formatTestResults(testResults));
    } catch (error: any) {
      setOutput(`测试运行失败: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  // 运行单个测试
  const runSingleTest = async (testName: string) => {
    setIsRunning(true);
    setOutput(`正在运行 ${testName} 测试...\n`);

    try {
      let testResults: TestResult[] = [];
      
      switch (testName) {
        case 'connection':
          testResults = [await testDatabaseConnection()];
          break;
        case 'auth':
          testResults = [await testAuthentication()];
          break;
        case 'profile':
          testResults = await testUserProfileCRUD();
          break;
        case 'experience':
          testResults = await testExperienceCRUD();
          break;
      }
      
      setResults(testResults);
      setOutput(formatTestResults(testResults));
    } catch (error: any) {
      setOutput(`测试运行失败: ${error.message}`);
    } finally {
      setIsRunning(false);
    }
  };

  // 清除结果
  const clearResults = () => {
    setResults([]);
    setOutput('');
  };

  // 开发环境检查
  if (process.env.NODE_ENV !== 'development') {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              页面不可用
            </h1>
            <p className="text-gray-600">
              此页面仅在开发环境中可用
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            {/* 页面头部 */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                数据库集成测试
              </h1>
              <p className="text-gray-600">
                测试前端与Supabase数据库的集成，验证CRUD操作是否正常工作
              </p>
            </div>

            {/* 测试控制面板 */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">测试控制</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <Button
                  onClick={runAllTests}
                  disabled={isRunning}
                  className="w-full"
                >
                  {isRunning ? (
                    <div className="flex items-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      运行中...
                    </div>
                  ) : (
                    '运行所有测试'
                  )}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => runSingleTest('connection')}
                  disabled={isRunning}
                  className="w-full"
                >
                  测试数据库连接
                </Button>

                <Button
                  variant="outline"
                  onClick={() => runSingleTest('auth')}
                  disabled={isRunning}
                  className="w-full"
                >
                  测试用户认证
                </Button>

                <Button
                  variant="outline"
                  onClick={() => runSingleTest('profile')}
                  disabled={isRunning}
                  className="w-full"
                >
                  测试用户资料CRUD
                </Button>

                <Button
                  variant="outline"
                  onClick={() => runSingleTest('experience')}
                  disabled={isRunning}
                  className="w-full"
                >
                  测试经历CRUD
                </Button>

                <Button
                  variant="ghost"
                  onClick={clearResults}
                  disabled={isRunning}
                  className="w-full"
                >
                  清除结果
                </Button>
              </div>
            </div>

            {/* 测试结果概览 */}
            {results.length > 0 && (
              <div className="bg-white rounded-lg shadow p-6 mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">测试结果概览</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">
                      {results.length}
                    </div>
                    <div className="text-sm text-gray-600">总测试数</div>
                  </div>
                  
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {results.filter(r => r.success).length}
                    </div>
                    <div className="text-sm text-gray-600">通过</div>
                  </div>
                  
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {results.filter(r => !r.success).length}
                    </div>
                    <div className="text-sm text-gray-600">失败</div>
                  </div>
                </div>

                {/* 详细结果 */}
                <div className="space-y-3">
                  {results.map((result, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border ${
                        result.success 
                          ? 'bg-green-50 border-green-200' 
                          : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          {result.success ? (
                            <svg className="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg className="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <div className="flex-1">
                          <h3 className={`font-medium ${
                            result.success ? 'text-green-900' : 'text-red-900'
                          }`}>
                            {result.test}
                          </h3>
                          <p className={`text-sm ${
                            result.success ? 'text-green-700' : 'text-red-700'
                          }`}>
                            {result.message}
                          </p>
                          {result.data && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-xs text-gray-600">
                                查看数据
                              </summary>
                              <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                                {JSON.stringify(result.data, null, 2)}
                              </pre>
                            </details>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 原始输出 */}
            {output && (
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">原始输出</h2>
                <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto whitespace-pre-wrap">
                  {output}
                </pre>
              </div>
            )}

            {/* 使用说明 */}
            <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                📋 使用说明
              </h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 确保您已登录并且Supabase数据库正在运行</li>
                <li>• "运行所有测试"会依次执行所有测试项目</li>
                <li>• 单独测试可以针对特定功能进行验证</li>
                <li>• 测试会创建和删除临时数据，不会影响您的实际数据</li>
                <li>• 如果测试失败，请检查数据库连接和权限配置</li>
              </ul>
            </div>
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
