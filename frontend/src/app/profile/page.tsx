/**
 * 个人资料页面
 * 
 * 用户可以在此页面查看和编辑个人信息
 * 包括基本信息、联系方式、订阅状态等
 */

'use client';

import { useState, useEffect } from 'react';
import { useAuthStore } from '@/store/auth';
import { AuthGuard } from '@/components/auth/auth-guard';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading';
import { updateUserProfile } from '@/lib/database';
import { isValidEmail } from '@/utils/validation';

/**
 * 个人资料页面组件
 */
export default function ProfilePage() {
  const { user, setUser } = useAuthStore();
  
  const [formData, setFormData] = useState({
    full_name: '',
    title: '',
    email: '',
    phone: '',
    location: '',
    website: '',
    linkedin: '',
    github: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // 初始化表单数据
  useEffect(() => {
    if (user) {
      setFormData({
        full_name: user.full_name || '',
        title: user.title || '',
        email: user.email,
        phone: (user.contact_info as any)?.phone || '',
        location: (user.contact_info as any)?.location || '',
        website: (user.contact_info as any)?.website || '',
        linkedin: (user.contact_info as any)?.linkedin || '',
        github: (user.contact_info as any)?.github || ''
      });
    }
  }, [user]);

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除对应字段的错误
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: "" }));
    }
    
    // 清除全局消息
    if (error) setError('');
    if (success) setSuccess('');
  };

  // 验证表单
  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.full_name.trim()) {
      errors.full_name = "姓名是必填项";
    }

    if (!formData.email) {
      errors.email = "邮箱是必填项";
    } else if (!isValidEmail(formData.email)) {
      errors.email = "请输入有效的邮箱地址";
    }

    // 验证网址格式
    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      errors.website = "请输入有效的网址（以http://或https://开头）";
    }

    if (formData.linkedin && !formData.linkedin.match(/^https?:\/\/(www\.)?linkedin\.com\/.+/)) {
      errors.linkedin = "请输入有效的LinkedIn链接";
    }

    if (formData.github && !formData.github.match(/^https?:\/\/(www\.)?github\.com\/.+/)) {
      errors.github = "请输入有效的GitHub链接";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !user) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      // 构建联系信息对象
      const contact_info = {
        phone: formData.phone || undefined,
        location: formData.location || undefined,
        website: formData.website || undefined,
        linkedin: formData.linkedin || undefined,
        github: formData.github || undefined
      };

      // 更新用户资料
      const updatedProfile = await updateUserProfile(user.user_id, {
        full_name: formData.full_name || undefined,
        title: formData.title || undefined,
        contact_info
      });

      if (updatedProfile) {
        // 更新本地用户状态
        const updatedUser = {
          ...user,
          full_name: updatedProfile.full_name || undefined,
          title: updatedProfile.title || undefined,
          contact_info: updatedProfile.contact_info as Record<string, unknown> || {}
        };
        
        setUser(updatedUser);
        setSuccess('个人资料更新成功！');
      } else {
        setError('更新失败，请稍后重试');
      }
    } catch (error: any) {
      console.error('更新个人资料失败:', error);
      setError(error.message || '更新失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <AuthGuard>
        <MainLayout>
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-64">
              <LoadingSpinner size="lg" />
            </div>
          </div>
        </MainLayout>
      </AuthGuard>
    );
  }

  return (
    <AuthGuard>
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* 页面头部 */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                个人资料
              </h1>
              <p className="text-gray-600">
                管理您的个人信息和联系方式
              </p>
            </div>

            {/* 表单 */}
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* 基本信息 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">基本信息</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    name="full_name"
                    label="姓名"
                    value={formData.full_name}
                    onChange={handleInputChange}
                    error={formErrors.full_name}
                    placeholder="请输入您的姓名"
                    required
                  />

                  <Input
                    name="title"
                    label="职业头衔"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="如：高级软件工程师"
                  />

                  <Input
                    name="email"
                    label="邮箱地址"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    error={formErrors.email}
                    placeholder="请输入邮箱地址"
                    required
                    disabled
                    helperText="邮箱地址不可修改"
                  />

                  <Input
                    name="phone"
                    label="手机号码"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="请输入手机号码"
                  />
                </div>
              </div>

              {/* 联系信息 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">联系信息</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    name="location"
                    label="所在地"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="如：北京市朝阳区"
                  />

                  <Input
                    name="website"
                    label="个人网站"
                    value={formData.website}
                    onChange={handleInputChange}
                    error={formErrors.website}
                    placeholder="https://yourwebsite.com"
                  />

                  <Input
                    name="linkedin"
                    label="LinkedIn"
                    value={formData.linkedin}
                    onChange={handleInputChange}
                    error={formErrors.linkedin}
                    placeholder="https://linkedin.com/in/yourprofile"
                  />

                  <Input
                    name="github"
                    label="GitHub"
                    value={formData.github}
                    onChange={handleInputChange}
                    error={formErrors.github}
                    placeholder="https://github.com/yourusername"
                  />
                </div>
              </div>

              {/* 订阅信息 */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">订阅信息</h2>
                
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900">当前订阅状态</h3>
                    <p className="text-sm text-gray-600">
                      您当前的订阅计划和状态
                    </p>
                  </div>
                  <div className="text-right">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      user.subscription_status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : user.subscription_status === 'trial'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.subscription_status === 'active'
                        ? '付费用户'
                        : user.subscription_status === 'trial'
                        ? '试用中'
                        : '免费用户'}
                    </span>
                    {user.subscription_status !== 'active' && (
                      <div className="mt-2">
                        <Button variant="outline" size="sm">
                          升级订阅
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 全局消息 */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              {success && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-green-800">{success}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* 提交按钮 */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <LoadingSpinner size="sm" className="mr-2" />
                      保存中...
                    </div>
                  ) : (
                    '保存更改'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </MainLayout>
    </AuthGuard>
  );
}
