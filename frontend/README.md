# SynapseCV Frontend

## 项目概述

SynapseCV前端是基于Next.js 14构建的现代化Web应用，采用TypeScript、Tailwind CSS和PWA技术栈，为AI驱动的简历优化平台提供用户界面。

## 技术栈

- **框架**: Next.js 14 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **UI组件**: 自定义组件库
- **数据库**: Supabase (PostgreSQL)
- **支付**: Stripe
- **PWA**: next-pwa
- **测试**: Jest + React Testing Library

## 项目结构

```
frontend/
├── src/
│   ├── app/                    # Next.js App Router页面
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   ├── page.tsx           # 首页
│   │   ├── login/             # 登录页面
│   │   └── register/          # 注册页面
│   ├── components/            # 可复用组件
│   │   ├── ui/                # 基础UI组件
│   │   ├── layout/            # 布局组件
│   │   └── forms/             # 表单组件
│   ├── hooks/                 # 自定义Hooks
│   ├── store/                 # Zustand状态管理
│   ├── utils/                 # 工具函数
│   ├── types/                 # TypeScript类型定义
│   └── lib/                   # 第三方库配置
├── public/                    # 静态资源
├── tests/                     # 测试文件
├── .env.example              # 环境变量示例
├── next.config.js            # Next.js配置
├── tailwind.config.ts        # Tailwind配置
└── package.json              # 依赖配置
```

## 快速开始

### 环境要求

- Node.js 18.0+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 环境配置

1. 复制环境变量示例文件：
```bash
cp .env.example .env.local
```

2. 编辑 `.env.local` 文件，填入实际的配置值：
```env
# 数据库配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# API配置
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1

# 其他配置...
```

### 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 开发指南

### 代码规范

- 使用TypeScript进行类型检查
- 遵循ESLint规则
- 使用Prettier进行代码格式化
- 组件使用PascalCase命名
- 文件使用kebab-case命名

### 组件开发

1. **基础UI组件** (`src/components/ui/`)
   - 可复用的基础组件
   - 使用Tailwind CSS样式
   - 支持无障碍性

2. **布局组件** (`src/components/layout/`)
   - 页面布局相关组件
   - 头部、底部、侧边栏等

3. **表单组件** (`src/components/forms/`)
   - 表单相关组件
   - 验证逻辑集成

### 状态管理

使用Zustand进行状态管理：

```typescript
// 创建store
import { create } from 'zustand';

interface AppState {
  count: number;
  increment: () => void;
}

export const useAppStore = create<AppState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
}));

// 在组件中使用
const { count, increment } = useAppStore();
```

### 样式开发

1. **Tailwind CSS类名**
   - 优先使用Tailwind工具类
   - 自定义样式放在`globals.css`中

2. **响应式设计**
   - 移动优先的设计原则
   - 使用Tailwind响应式前缀

3. **主题系统**
   - 使用CSS变量定义主题
   - 支持明暗主题切换

### API集成

1. **Supabase客户端**
   ```typescript
   import { supabase } from '@/lib/supabase';
   
   // 查询数据
   const { data, error } = await supabase
     .from('table_name')
     .select('*');
   ```

2. **API路由**
   - 使用Next.js API路由
   - 统一的错误处理

### 测试

```bash
# 运行测试
npm test

# 运行测试覆盖率
npm run test:coverage

# 运行E2E测试
npm run test:e2e
```

## 构建和部署

### 构建应用

```bash
npm run build
```

### 启动生产服务器

```bash
npm start
```

### 部署

支持多种部署方式：

1. **Vercel** (推荐)
   ```bash
   npm i -g vercel
   vercel
   ```

2. **Netlify**
   - 连接GitHub仓库
   - 自动构建和部署

3. **Docker**
   ```bash
   docker build -t synapsecv-frontend .
   docker run -p 3000:3000 synapsecv-frontend
   ```

## 性能优化

### 代码分割

- 使用动态导入进行代码分割
- 路由级别的代码分割

### 图片优化

- 使用Next.js Image组件
- 支持WebP和AVIF格式

### 缓存策略

- 静态资源缓存
- API响应缓存
- 浏览器缓存优化

## 无障碍性

- 遵循WCAG 2.1 Level AA标准
- 支持键盘导航
- 屏幕阅读器友好
- 语义化HTML结构

## 浏览器支持

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 故障排除

### 常见问题

1. **环境变量未生效**
   - 确保变量名以`NEXT_PUBLIC_`开头
   - 重启开发服务器

2. **样式不生效**
   - 检查Tailwind配置
   - 清除浏览器缓存
   - 确保PostCSS配置正确使用`@tailwindcss/postcss`
   - 重启开发服务器
   - 访问`/style-test`页面验证样式功能

3. **构建失败**
   - 检查TypeScript错误
   - 确保所有依赖已安装

### 调试工具

- React Developer Tools
- Next.js DevTools
- Tailwind CSS IntelliSense

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 更新日志

### v1.0.1 (2024-12-19) - 样式修复版本
- **修复PostCSS配置问题**
  - 更新`postcss.config.mjs`使用`@tailwindcss/postcss`插件
  - 添加`autoprefixer`和`postcss`依赖
- **修复Tailwind CSS配置**
  - 暂时注释掉Tailwind插件以避免依赖冲突
  - 确保基础Tailwind功能正常工作
- **删除重复配置文件**
  - 移除重复的`next.config.ts`文件
  - 统一使用`next.config.js`配置
- **添加样式测试页面**
  - 创建`/style-test`页面用于验证样式功能
  - 包含颜色、按钮、布局、文本、间距、动画等测试
- **构建和开发服务器修复**
  - 修复Turbopack构建错误
  - 确保开发服务器正常启动
  - 验证样式正确应用

### v1.0.0 (2024-12-19)
- 初始版本发布
- 基础架构搭建
- 核心功能实现