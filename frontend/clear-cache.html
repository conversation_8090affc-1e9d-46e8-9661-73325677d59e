<!DOCTYPE html>
<html>
<head>
    <title>清理Service Worker缓存</title>
</head>
<body>
    <h1>清理Service Worker缓存</h1>
    <button onclick="clearServiceWorkers()">清理Service Worker</button>
    <button onclick="clearAllCaches()">清理所有缓存</button>
    <div id="status"></div>

    <script>
        async function clearServiceWorkers() {
            const status = document.getElementById('status');
            status.innerHTML = '正在清理Service Worker...';
            
            try {
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    console.log('找到的Service Worker注册:', registrations);
                    
                    for (let registration of registrations) {
                        await registration.unregister();
                        console.log('已注销Service Worker:', registration.scope);
                    }
                    
                    status.innerHTML = `已清理 ${registrations.length} 个Service Worker注册`;
                } else {
                    status.innerHTML = '浏览器不支持Service Worker';
                }
            } catch (error) {
                console.error('清理Service Worker时出错:', error);
                status.innerHTML = '清理失败: ' + error.message;
            }
        }

        async function clearAllCaches() {
            const status = document.getElementById('status');
            status.innerHTML = '正在清理所有缓存...';
            
            try {
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    console.log('找到的缓存:', cacheNames);
                    
                    for (let cacheName of cacheNames) {
                        await caches.delete(cacheName);
                        console.log('已删除缓存:', cacheName);
                    }
                    
                    status.innerHTML = `已清理 ${cacheNames.length} 个缓存`;
                } else {
                    status.innerHTML = '浏览器不支持Cache API';
                }
            } catch (error) {
                console.error('清理缓存时出错:', error);
                status.innerHTML = '清理失败: ' + error.message;
            }
        }

        // 页面加载时显示当前状态
        window.onload = async function() {
            const status = document.getElementById('status');
            
            if ('serviceWorker' in navigator) {
                const registrations = await navigator.serviceWorker.getRegistrations();
                if (registrations.length > 0) {
                    status.innerHTML = `检测到 ${registrations.length} 个Service Worker注册。请点击按钮清理。`;
                } else {
                    status.innerHTML = '没有检测到Service Worker注册。';
                }
            } else {
                status.innerHTML = '浏览器不支持Service Worker。';
            }
        };
    </script>
</body>
</html>
