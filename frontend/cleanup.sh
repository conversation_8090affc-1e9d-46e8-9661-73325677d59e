#!/bin/bash

echo "🧹 开始清理SynapseCV前端缓存和Service Worker..."

# 清理Next.js构建缓存
echo "清理.next目录..."
rm -rf .next

# 清理node_modules缓存
echo "清理node_modules缓存..."
rm -rf node_modules/.cache

# 清理可能的Service Worker文件
echo "清理Service Worker文件..."
rm -rf public/sw.js
rm -rf public/workbox-*.js
rm -rf public/app-build-manifest.json
rm -rf public/manifest.json

# 清理可能的PWA相关文件
echo "清理PWA相关文件..."
find public -name "*.js" -exec grep -l "serviceWorker\|workbox" {} \; 2>/dev/null | xargs rm -f

# 清理浏览器可能缓存的manifest文件
echo "清理manifest文件..."
find . -name "manifest.json" -delete
find . -name "*.webmanifest" -delete

# 清理可能的缓存目录
echo "清理缓存目录..."
rm -rf .cache
rm -rf dist
rm -rf build

# 清理临时文件
echo "清理临时文件..."
rm -rf *.log
rm -rf .DS_Store

echo "✅ 清理完成！"
echo ""
echo "📋 接下来请执行以下步骤："
echo "1. 在浏览器中打开 http://localhost:3000/clear-cache.html"
echo "2. 点击'清理Service Worker'和'清理所有缓存'按钮"
echo "3. 关闭浏览器开发者工具"
echo "4. 重新启动开发服务器: npm run dev"
echo ""
echo "如果问题仍然存在，请："
echo "1. 在浏览器中按F12打开开发者工具"
echo "2. 进入Application标签页"
echo "3. 在左侧找到'Service Workers'，点击并注销所有注册"
echo "4. 在左侧找到'Storage'，点击'Clear storage'清理所有数据"
