# **原型与用户流程规划框架**

文件名称： ResumeForge Pro \- 原型与用户流程规划 V1.0

创建人：

创建日期： 2025-10-06

关联 PRD/SRS： ResumeForge Pro V1.0

## **1\. 原型资产概览**

| 类型 | 状态 | 制作工具 | 访问链接 |
| :---- | :---- | :---- | :---- |
| **低保真线框图** | 已完成草稿 | Markdown/Canvas | (当前文件) |
| **高保真交互稿** | 待设计 | Figma/Sketch |  |

## **2\. 核心用户流程（User Flows）**

### **2.1 流程 A：首次录入经历与 AI 优化**

| 步骤 | 界面名称 | 用户操作 | 系统响应 (界面变化) | 关联需求 ID |
| :---- | :---- | :---- | :---- | :---- |
| **1** | **主 Profile 页 \- 经历列表** | 点击 “添加新项目经历” | 弹出 **“经历事实录入”** 模态框。 | FR-B-001 |
| **2** | **经历事实录入 (模态框)** | 录入**原始职责**、**技术栈**（如：Go, PostgreSQL）和**原始量化结果**（如：延迟从 500ms 降到 100ms）。 | 数据暂存为 。 | FR-B-001 |
| **3** | **经历事实录入 (模态框)** | 点击 **“AI 优化并生成 Bullet Points”** 按钮。 | 按钮显示 Loading 状态； 调用  。 | FR-B-002 |
| **4** | **AI 优化结果预览区** | **实时显示**：左侧为**原始事实**，右侧为 **AI 优化后的 Bullet Points**（如：Engineered... reducing latency by 80%）。 | 自动填充  数组。 | FR-B-002 |
| **5** | **经历事实录入 (模态框)** | 用户选择 **“接受 AI 优化”** 并点击 **“保存”**。 | 经历被保存到  表；模态框关闭。 | FR-B-001 |

## **3\. 界面清单与状态管理（Screen Inventory & State）**

### **3.1 界面草稿：经历事实录入模态框**

**目的：** 确保原子化数据录入。

| 区域 | 组件/内容 | 关键交互/状态 |
| :---- | :---- | :---- |
| **标题** | “录入项目经历事实” |  |
| **输入区 A** | **公司/职位/时间** | 标准文本输入框。 |
| **输入区 B (核心)** | **原始职责描述**（多行文本） | 鼓励用户用自然语言描述做了什么。 |
| **输入区 C** | **技术栈/关键词** | Tag 输入组件（例如：输入 React 后回车生成一个标签）。 |
| **输入区 D** | **原始量化结果** | 鼓励用户只输入数字和百分比的原始数据。 |
| **操作区** | **“AI 优化”** 按钮 | **Loading 状态**：显示“AI 正在为您定制 SDE 级描述...”。 |
| **预览区** | **优化对比**（左右分栏） | **AI 结果状态：** 成功生成、生成失败（显示降级提示）。 |

### **3.2 界面草稿：简历管理主控台**

**布局：** 经典三栏布局。

| 栏目 | 模块 | 关键功能 |
| :---- | :---- | :---- |
| **左侧导航 (15%)** | 导航菜单 | Profile, Master Experiences, Resume Versions, Settings. |
| **中央工作区 (45%)** | **经历列表 / 版本编辑区** | 列表展示 。点击经历可进入编辑。下方是**版本配置卡片**。 |
| **右侧预览区 (40%)** | **实时简历预览** | 默认展示当前选定的  的  渲染效果。 |

## **4\. 反馈与迭代记录**

* **2025-10-06：** 确定了  \+  的技术栈。前端设计需要保证  **调用失败时，用户体验能平稳降级**（例如：提示  失败，但用户仍可手动编辑 ）。

## **5\. 核心用户流程（User Flows）**

### **5.1 流程 B：创建和管理动态简历版本**

| 步骤 | 界面名称 | 用户操作 | 系统响应 (界面变化) | 关联需求 ID |
| :---- | :---- | :---- | :---- | :---- |
| **1** | **版本配置区** | 点击 “新建简历版本”，命名为 “Backend SDE \- FAANG” | 创建  新记录，跳转到版本配置界面。 | FR-B-003 |
| **2** | **版本配置界面** | **(核心操作)：** 从 Master 经历列表中，勾选 **“包含此经历”**（例如：勾选 ）。 |   中  字段更新。 | FR-B-003 |
| **3** | **版本配置界面** | **Bullet Point 定制：** 选择  下的 **Bullet Points 焦点**为  或 。 |  中  或其他引用字段更新。 | FR-B-003 |
| **4** | **右侧预览区** | 实时查看  预览 | **内容动态变化：** 仅显示勾选的经历和定制化的 。 | FR-B-004 |
| **5** | **右侧预览区** | 再次回到 **Master Profile** 中，修改  的**原始量化结果**。 |  更新；**所有版本**的预览区**实时更新**内容。 | FR-B-004 |

## **6\. 界面清单与状态管理（Screen Inventory & State）**

### **6.1 界面草稿：版本配置卡片 (位于中央工作区)**

**目的：** 提供清晰的版本定制逻辑。

| 区域 | 组件/内容 | 关键交互/状态 |
| :---- | :---- | :---- |
| **版本信息** | 版本名称、目标职位、目标公司类型 | 可编辑。 |
| **模板选择** | **模板 ID 选择器** |  个精选模板缩略图。点击切换模板，实时更新右侧预览。 |
| **经历选择列表** | Master 经历列表（例如：  \- Tech Innovators） | **多选复选框：** 用于控制 。 |
| **焦点定制下拉菜单** | 针对每个被选中的经历：下拉选择**AI 优化焦点**。 | 选项包括：“ **通用**”、“**数据分析**”、“**管理领导力**”等。 |
| **操作区** | **导出 PDF** 按钮 | Loading 状态：显示“正在渲染  页面...”。 |

### **6.2 关键交互：动态同步验证**

* **测试场景：** 用户在 **Master Experience** 中将  改为 。  
* **预期系统响应：**  
  1.  表中的  更新。  
  2. 后端  自动触发**轻量级  优化**，更新 。  
  3. 右侧预览区立即刷新，两个不同的简历版本（如  和    版本）中，该经历的技术栈都已更新。

## **7\. 体验设计注意事项**

* **视觉风格：** 必须采用**极简主义** ()、**高对比度**的设计，避免使用花哨的颜色或复杂的图标，以迎合  群体的专业审美。  
* **速度体感：** **版本切换**和**实时预览刷新**必须在  **毫秒内完成**，以体现“动态同步”的流畅性。