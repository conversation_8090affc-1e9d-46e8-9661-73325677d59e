# **SynapseCV - API 契约 V1.0**

文件名称： SynapseCV - API 契约 V1.0  
创建人： 后端开发团队  
创建日期： 2024-12-19  
关联 SRS/版本： SynapseCV - 软件需求规格说明书 V1.0

## **1. 核心数据模型 (Data Model)**

定义系统中的核心数据实体及其关系，作为数据库设计和 API 响应的基础。

| 实体名称 | 关键字段 (类型) | 关系 (FK) | 描述 |
| :---- | :---- | :---- | :---- |
| **ResumeProfile** | user_id (PK, UUID), experience (JSONB), created_at, updated_at | User(1:1) | 用户主数据源，包含所有原始经历信息 |
| **ExperienceFact** | exp_id (PK, UUID), raw_facts (TEXT), optimized_bullets (JSONB), target_roles (JSONB) | ResumeProfile(1:N) | 单个项目经历的原始事实和AI优化结果 |
| **ResumeVersion** | version_id (PK, UUID), user_id (FK), included_experience_ids (JSONB), version_name (VARCHAR), version_config (JSONB) | User(1:N) | 简历版本，引用特定的经历ID，配置存储在JSONB中 |
| **User** | user_id (PK, UUID), email (VARCHAR), password_hash (VARCHAR), subscription_status (ENUM) | N/A | 用户基本信息 |
| **Subscription** | subscription_id (PK, UUID), user_id (FK), stripe_subscription_id (VARCHAR), status (ENUM), current_period_end (TIMESTAMP) | User(1:1) | 用户订阅信息 |

## **2. API 接口合同 (API Contract)**

使用 OpenAPI 规范详细描述所有前后端交互的接口。

### **2.1 认证与授权 (Authentication & Authorization)**

* **认证机制：** 基于 JWT Token 的 Bearer 认证  
* **授权机制：** 基于用户身份的访问控制，用户只能访问自己的数据

### **2.2 接口列表**

#### **2.2.1 用户认证接口**

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | POST /api/v1/auth/register |
| **描述** | 用户注册 |
| **权限要求** | 无需认证 |
| **请求体** | { "email": "<EMAIL>", "password": "securePassword123" } |
| **成功响应 (201)** | { "user_id": "uuid", "email": "<EMAIL>", "token": "jwt_token" } |
| **失败响应 (400)** | { "error_code": 4001, "message": "Invalid email format" } |

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | POST /api/v1/auth/login |
| **描述** | 用户登录 |
| **权限要求** | 无需认证 |
| **请求体** | { "email": "<EMAIL>", "password": "securePassword123" } |
| **成功响应 (200)** | { "user_id": "uuid", "email": "<EMAIL>", "token": "jwt_token" } |
| **失败响应 (401)** | { "error_code": 4010, "message": "Invalid credentials" } |

#### **2.2.2 简历数据管理接口**

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | POST /api/v1/profile/experience |
| **描述** | 添加新的项目经历 |
| **权限要求** | 认证用户 |
| **请求体** | { "raw_facts": "Developed a web application using React and Node.js", "technologies": ["React", "Node.js"], "quantified_data": "Improved performance by 30%" } |
| **成功响应 (201)** | { "exp_id": "uuid", "status": "created" } |
| **失败响应 (400)** | { "error_code": 4001, "message": "Missing required fields" } |

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | POST /api/v1/profile/optimize |
| **描述** | 触发 AI 优化原始事实 |
| **权限要求** | 认证用户 |
| **请求体** | { "fact_id": "uuid", "target_focus": "SDE_General" } |
| **成功响应 (200)** | { "optimized_bullets": ["Developed a scalable web application using React and Node.js, resulting in 30% performance improvement"], "status": "optimized" } |
| **失败响应 (500)** | { "error_code": 5001, "message": "AI optimization failed" } |

#### **2.2.3 简历版本管理接口**

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | POST /api/v1/resume/version |
| **描述** | 创建新的简历版本 |
| **权限要求** | 认证用户 |
| **请求体** | { "version_name": "Google Application", "included_experience_ids": ["uuid1", "uuid2"] } |
| **成功响应 (201)** | { "version_id": "uuid", "version_name": "Google Application", "status": "created" } |
| **失败响应 (400)** | { "error_code": 4001, "message": "Invalid experience IDs" } |

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | GET /api/v1/resume/render/{version_id} |
| **描述** | 根据版本 ID 动态渲染并返回 PDF 文件流 |
| **权限要求** | 认证用户 |
| **请求参数** | **Path:** version_id (UUID) |
| **成功响应 (200)** | PDF 文件流 (Content-Type: application/pdf) |
| **失败响应 (404)** | { "error_code": 4004, "message": "Version not found" } |

#### **2.2.4 支付与订阅接口**

| 属性 | 详情 |
| :---- | :---- |
| **路径 (URL)** | POST /api/v1/payment/create-subscription |
| **描述** | 创建 Stripe 订阅 |
| **权限要求** | 认证用户 |
| **请求体** | { "payment_method_id": "pm_xxx", "price_id": "price_monthly_19" } |
| **成功响应 (200)** | { "subscription_id": "sub_xxx", "status": "active", "current_period_end": "2024-01-19T00:00:00Z" } |
| **失败响应 (400)** | { "error_code": 4001, "message": "Payment method invalid" } |

## **3. 业务逻辑实现规范**

### **3.1 核心业务流程**

详细描述复杂业务流程的**处理逻辑**和**状态转换**：

* **AI 优化流程：** 1. 验证用户权限和 fact_id；2. 获取原始事实数据；3. 根据 target_focus 选择优化策略；4. 调用 OpenAI GPT-4 和 Perplexity API 进行优化；5. 存储优化结果；6. 返回优化后的 Bullet Points。

* **动态版本生成流程：** 1. 验证用户权限；2. 检查包含的经历 ID 是否属于当前用户；3. 创建新版本记录；4. 建立版本与经历的关联；5. 返回版本信息。

* **PDF 渲染流程：** 1. 验证用户权限和版本所有权；2. 获取版本关联的经历数据；3. 应用 ATS 模板；4. 生成 PDF 文件；5. 返回文件流。

### **3.2 标准错误码定义**

定义系统使用的统一错误码，便于前后端定位问题。

| 状态码 (HTTP) | 自定义 Error Code | 描述 | 适用场景 |
| :---- | :---- | :---- | :---- |
| **400 Bad Request** | 4001 | 输入参数验证失败 | 邮箱格式错误、必填项缺失 |
| **401 Unauthorized** | 4010 | 认证失败或 Token 无效 | 未提供 Token 或 Token 过期 |
| **403 Forbidden** | 4030 | 权限不足 | 试图访问他人数据 |
| **404 Not Found** | 4004 | 资源未找到 | URL 路径错误、数据 ID 不存在 |
| **429 Too Many Requests** | 4290 | 请求频率超限 | AI 优化请求过于频繁 |
| **500 Internal Server Error** | 5000 | 未知系统错误 | 数据库连接失败、代码运行时异常 |
| **500 Internal Server Error** | 5001 | AI 服务错误 | LLM API 调用失败或超时 |
| **503 Service Unavailable** | 5030 | 服务暂时不可用 | 外部服务（Stripe、LLM）不可用 |

## **4. 数据库实现与安全**

### **4.1 数据库迁移 (Migration) 策略**

* **工具：** Supabase 内置迁移系统  
* **命名规范：** 迁移文件命名格式：YYYYMMDD_HHMMSS_description.sql  
* **回滚机制：** 每个迁移必须提供可安全回滚的脚本，使用 Supabase 的版本控制功能

### **4.2 隐私与安全实现**

* **数据加密：** 简历内容使用 AES-256 加密存储，密钥通过环境变量管理  
* **数据脱敏：** 日志中不记录完整的简历内容，仅记录操作类型和 ID  
* **输入验证：** 所有用户输入必须进行严格的**服务器端**验证和清理，防止注入攻击  
* **访问控制：** 使用 Row Level Security (RLS) 确保用户只能访问自己的数据

### **4.3 API 安全措施**

* **速率限制：** AI 优化接口每分钟最多 10 次请求  
* **请求大小限制：** 单个请求体最大 1MB  
* **CORS 配置：** 仅允许指定域名访问 API  
* **HTTPS 强制：** 所有 API 调用必须使用 HTTPS

## **5. 版本管理策略详细说明**

### **5.1 版本数量限制策略**

**付费用户限制：** 建议对简历版本数量 (resume_versions 表中的记录数) 不设限或设一个慷慨的高上限（例如 100 个版本）。

**理由：** 我们的核心收费点是 AI 优化的价值和数据同步的便利性，而非版本数量。限制数量会损害核心价值主张。

### **5.2 存储策略**

**核心设计：** 版本的核心数据存储在 resume_versions 表的 version_config JSONB 字段中。这个 JSONB 只存储引用 (References)，即引用了哪些 exp_id 和 opt_id。

**核心优势：** 由于只存储引用，无论用户创建多少个版本，底层存储开销都极小，且核心数据 (master_experiences) 保持单一事实来源 (SSOT)，保证了一改全改的效率。

### **5.3 版本同步机制**

* **主数据源变更：** 当用户修改 master_experiences 中的原始事实时，所有引用该经历的版本自动更新
* **AI 优化更新：** 当 AI 优化结果更新时，所有相关版本自动同步
* **版本独立性：** 每个版本可以独立配置显示顺序、模板选择等个性化设置

**后续步骤：** 前后端开发团队依据此契约并行开发，同时 DevOps 团队准备部署环境。