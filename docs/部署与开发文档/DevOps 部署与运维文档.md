# **SynapseCV - 部署与运维指南 V1.0**

文件名称： SynapseCV - 部署与运维指南 V1.0  
创建人： DevOps 团队  
创建日期： 2024-12-19  
关联版本： v1.0.0-mvp

## **1. CI/CD 管道定义 (Continuous Integration/Continuous Delivery)**

### **1.1 自动化流程概述**

* **工具：** GitHub Actions / GitLab CI  
* **触发条件：** Merge 到 main 分支时自动触发部署流程

| 阶段 | 任务 | 目标环境 | 持续时间 (SLO) |
| :---- | :---- | :---- | :---- |
| **集成 (CI)** | 依赖安装、代码 Linting、单元测试、构建 Artifact | N/A | < 5 分钟 |
| **部署 (CD)** | 容器化（Docker）、推送到注册中心、部署到 Staging 环境 | Staging | < 10 分钟 |
| **发布 (Release)** | 冒烟测试通过后，手动触发生产环境部署 | Production | < 15 分钟 |

### **1.2 环境配置模板**

* **容器化：** 
  - 前端 Dockerfile: `./frontend/Dockerfile`
  - 后端 Dockerfile: `./backend/Dockerfile`
* **环境变量：** 使用 GitHub Secrets 管理敏感配置信息

### **1.3 CI/CD 管道配置**

```yaml
# .github/workflows/deploy.yml
name: Deploy SynapseCV
on:
  push:
    branches: [main]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test
      - name: Build application
        run: npm run build
  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to Staging
        run: echo "Deploy to staging environment"
```

## **2. 环境配置与访问**

### **2.1 环境列表**

| 环境名称 | 目标 | 访问 URL | 访问限制 |
| :---- | :---- | :---- | :---- |
| **开发 (Dev)** | 开发人员本地测试 | localhost:3000 | 仅本地访问 |
| **测试 (Staging)** | QA 测试和 UAT 验收 | https://staging.synapsecv.com | VPN 或 IP 白名单限制 |
| **生产 (Production)** | 最终用户 | https://synapsecv.com | 公网开放 |

### **2.2 数据库部署配置**

* **数据库服务：** Supabase (PostgreSQL)  
* **连接信息：** 通过环境变量管理，存储在 GitHub Secrets 中
* **备份策略：** Supabase 自动备份，每日增量备份，每周全量备份

### **2.3 部署环境配置**

**前端部署 (Vercel/Netlify):**
- 构建命令: `npm run build`
- 输出目录: `out`
- 环境变量: `NEXT_PUBLIC_API_URL`, `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`

**后端部署 (DigitalOcean Droplet/AWS EC2):**
- 容器化部署使用 Docker
- 环境变量: `DATABASE_URL`, `JWT_SECRET`, `OPENAI_API_KEY`, `PERPLEXITY_API_KEY`, `STRIPE_SECRET_KEY`

## **3. 监控、日志与告警 (Observability)**

### **3.1 日志收集策略**

* **日志级别：** 生产环境最低记录 INFO 级别，错误记录 ERROR 或 FATAL  
* **日志工具：** 使用 Supabase 内置日志系统，集成 Grafana 进行可视化
* **日志格式：** 结构化 JSON 格式，包含时间戳、级别、服务名、请求ID

### **3.2 关键性能指标 (Metrics) 监控**

| 指标名称 | 目标 SLO | 告警阈值 | 负责人 |
| :---- | :---- | :---- | :---- |
| **OpenAI GPT-4 API 错误率** | ≤ 1% | 错误率 > 5% 持续 5 分钟 | 后端团队 |
| **OpenAI GPT-4 调用延迟 (P95)** | ≤ 5 秒 | P95 延迟 > 10 秒持续 10 分钟 | 后端团队 |
| **Perplexity API 错误率** | ≤ 2% | 错误率 > 10% 持续 5 分钟 | 后端团队 |
| **Perplexity 调用延迟 (P95)** | ≤ 3 秒 | P95 延迟 > 6 秒持续 10 分钟 | 后端团队 |
| **API 错误率** | < 1% | 错误率 > 5% 持续 5 分钟 | 后端团队 |
| **API 延迟 (P95)** | < 300 ms | P95 延迟 > 500ms 持续 10 分钟 | 后端团队 |
| **系统 CPU 利用率** | < 70% | CPU > 85% 持续 15 分钟 | DevOps 团队 |
| **数据库连接数** | < 80% | 连接数 > 90% 持续 5 分钟 | DevOps 团队 |

### **3.3 告警与通知**

* **告警工具：** Supabase 内置监控 + 自定义 Prometheus 告警  
* **通知渠道：** 严重错误发送到 Slack 频道，一般告警发送到邮件组
* **告警级别：**
  - P0: 立即通知 (Slack + 邮件 + 短信)
  - P1: 15分钟内通知 (Slack + 邮件)
  - P2: 1小时内通知 (邮件)

## **4. 灾难恢复与回滚策略**

### **4.1 部署失败回滚**

* **回滚机制：** 如果部署后冒烟测试失败，CI/CD 管道应自动触发回滚到前一个稳定版本  
* **回滚时间目标 (RTO)：** 核心服务回滚时间不超过 5 分钟
* **冒烟测试检查项：**
  - 用户登录功能正常
  - AI 优化服务响应正常
  - PDF 生成功能正常
  - 支付流程正常

### **4.2 数据库恢复**

* **备份频率：** Supabase 自动每日增量备份，每周全量备份  
* **恢复流程：** 
  1. 登录 Supabase 控制台
  2. 选择目标时间点的备份
  3. 执行数据库恢复操作
  4. 验证数据完整性
* **恢复时间目标 (RTO)：** 数据库恢复时间不超过 4 小时

### **4.3 业务连续性规划**

* **服务降级方案：** 
  - AI 优化服务降级：OpenAI GPT-4 失败时，自动切换到 Perplexity API 或"仅使用用户录入的原始 Bullet Point"模式
  - 确保 PDF 生成功能不中断
* **故障转移：** 主要服务故障时，自动切换到备用服务或显示维护页面

## **5. 安全与合规**

### **5.1 安全配置**

* **HTTPS 强制：** 所有环境强制使用 HTTPS
* **防火墙配置：** 仅开放必要端口 (80, 443, 22)
* **访问控制：** 使用 SSH 密钥认证，禁用密码登录
* **定期安全更新：** 每月更新系统和依赖包

### **5.2 数据保护**

* **数据加密：** 传输和存储都使用 AES-256 加密
* **访问日志：** 记录所有敏感操作的访问日志
* **数据备份加密：** 所有备份数据都进行加密存储

### **5.3 安全工具选择**

**SAST (静态应用安全测试):**
- **工具：** Snyk 或 GitHub Advanced Security
- **应用：** 在 CI/CD 流程中运行，扫描 Python 依赖库的已知漏洞（SQLAlchemy, FastAPI libs）和代码中的安全弱点

**DAST (动态应用安全测试):**
- **工具：** OWASP ZAP (免费开源) 或 Burp Suite (专业)
- **应用：** 定期在 Staging 环境中模拟攻击（如 SQL 注入、跨站脚本），确保 FastAPI 的输入验证有效

### **5.4 监控工具选择**

**日志 (Logging):**
- **工具：** Grafana Loki / Cloudflare Logpush
- **应用：** 集中化日志收集和分析

**指标 (Metrics):**
- **工具：** Prometheus + Grafana / Supabase 内置的性能仪表盘
- **应用：** 系统性能监控和业务指标跟踪

**应用性能管理 (APM):**
- **工具：** Sentry
- **应用：** 错误跟踪和性能监控，特别适用于跟踪 AI API 的调用延迟和失败

### **5.5 备份策略**

**频率：**
- **数据库 (Supabase/PostgreSQL):** 每日全量备份 + 每小时增量备份
- **代码/配置：** Git 仓库管理

**恢复测试：** 每季度至少进行一次灾难恢复演练 (Disaster Recovery Drill)，确保能够从备份中恢复一个完整的生产环境（包括 Supabase 数据、FastAPI 服务和 n8n 配置）

**后续步骤：** 运维团队根据此指南搭建和维护 CI/CD 管道。