# **SDE 简历自动化平台 \- 数据库设计与契约 (Supabase/PostgreSQL)**

文件名称： ResumeForge Pro \- 数据库设计契约 V1.0

创建人：

创建日期： 2025-10-06

后端技术栈： FastAPI \+ Supabase (PostgreSQL)

## **1\. 实体关系图 ( Diagram)**

核心关系遵循 **1:N** 关系：

## **2\. 核心表格结构与 PostgreSQL 优化**

| 表名 | 目的 | 主键/外键/索引 | 核心字段 (类型) | PostgreSQL 优化要点 |  
| ----- | ----- | ----- | ----- | ----- |
| auth.users | Supabase 认证 | id (UUID, PK) | email (TEXT), created\_at (TIMESTAMPZ) | Supabase 内置： 用于 JWT Token 和用户身份验证。 |  
| public.user\_profiles | 用户基础信息 | user\_id (UUID, PK & FK auth.users.id) | full\_name (TEXT), title (TEXT), contact\_info (JSONB) | 数据安全： 强制启用行级安全 (RLS)。 JSONB 存储半结构化信息。 |  
| public.master\_experiences | 主经历事实表 (SSOT) | exp\_id (UUID, PK) | user\_id (UUID, FK), company\_name (TEXT), role\_title (TEXT), raw\_facts (JSONB, NOT NULL) | 核心： JSONB 存储用户录入的原子化原始事实。创建 GIN 索引 (raw\_facts) 以支持高性能关键词查询。 |  
| public.optimized\_content | AI 优化内容表 | opt\_id (UUID, PK) | exp\_id (UUID, FK), target\_focus (TEXT), optimized\_bullets (JSONB), ai\_model (TEXT) | 隔离性： 将 AI 生成结果与原始事实分离，方便 AI 模型版本迭代和 A/B 测试。 |  
| public.resume\_versions | 简历版本定义表 | version\_id (UUID, PK) | user\_id (UUID, FK), version\_name (TEXT), template\_id (TEXT), version\_config (JSONB) | 动态渲染： version\_config JSONB 字段存储版本映射逻辑（例如：包含哪些 exp\_id，使用哪个 optimized\_content）。 |  
| public.n8n\_automation\_log | n8n 自动化日志 | log\_id (UUID, PK) | user\_id (UUID, FK), event\_type (TEXT), payload (JSONB), status (TEXT) | 运营： 记录 n8n 触发的所有自动化事件，支持后续运营分析。 |

## **3\. 数据一致性与安全 (ACID 原则)**

### **3.1 核心原则**

* **原子性 ():** 在  后端使用  开启**事务**，确保对  的更新和随后的  的生成要么全部完成，要么全部回滚。  
* **隔离性 ():** **强制启用**  的 **Row Level Security ()**，确保每个用户都只能通过  访问自己的数据。  
* **一致性 ():** 严格的外键约束（例如： 引用 ）保证数据的引用完整性。  
* **持久性 ():** 由  提供的托管服务和备份机制保证。

### **3.2 关键 SQL 优化**

1. **JSONB GIN 索引 (高性能查询):**  
   \-- 提高 raw\_facts 字段内关键词搜索的效率  
   CREATE INDEX idx\_master\_experiences\_raw\_facts ON public.master\_experiences USING GIN (raw\_facts);

2. **RLS 安全策略 (示例):**  
   \-- 启用 RLS  
   ALTER TABLE public.master\_experiences ENABLE ROW LEVEL SECURITY;

   \-- 策略：用户只能 select/insert/update 自己的经历数据  
   CREATE POLICY user\_access\_only ON public.master\_experiences  
     FOR ALL  
     USING (auth.uid() \= user\_id);

## **4\. FastAPI 后端实现契约**

* **数据校验：**  必须使用  模型，严格校验进入   字段的  结构。  
* **事务管理：** 涉及数据修改和  调用的   接口，必须进行事务处理。