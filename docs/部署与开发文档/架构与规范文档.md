# **SynapseCV - 架构与规范 V1.0**

文件名称： SynapseCV - 架构与规范 V1.0  
创建人： 架构团队  
创建日期： 2024-12-19  
关联 SRS/版本： SynapseCV - 软件需求规格说明书 V1.0

## **1. 架构决策记录 (Architecture Decision Records - ADR)**

本节记录项目中的关键技术决策，包括选择的理由和替代方案。

### **1.1 技术栈选型决策**

| 决策 ID | 决策项 | 选择方案 | 替代方案 | 决策理由 |
| :---- | :---- | :---- | :---- | :---- |
| **ADR-001** | 后端框架 | Python/FastAPI | Node.js/Express | 更适合 AI 集成，异步处理能力强，API 文档自动生成 |
| **ADR-002** | 状态管理 | React + Zustand | Redux/MobX | 轻量级，独立开发友好，减少样板代码 |
| **ADR-003** | 数据库类型 | PostgreSQL (Supabase) | MySQL/MongoDB | 强大的 ACID 支持和 JSONB 字段，非常适合存储结构化简历数据 |
| **ADR-004** | 前端框架 | Next.js | Create React App/Vite | 全栈框架，SSR 支持，部署简单 |
| **ADR-005** | AI 服务集成 | OpenAI GPT-4 (主) + Perplexity (辅) | Claude/本地模型 | GPT-4 在文本生成方面表现最佳，Perplexity 提供实时信息补充 |
| **ADR-006** | 支付服务 | Stripe | PayPal/Square | 开发者友好，订阅管理完善，国际化支持好 |
| **ADR-007** | 移动端策略 | PWA (Progressive Web App) | 原生应用 | 独立开发者效率优先，一个代码库支持多端，维护成本低 |

### **1.2 核心架构图**

**系统架构概览：**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │   FastAPI       │    │   Supabase      │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (PostgreSQL)  │
│   + PWA         │    │   + Python      │    │   + JSONB       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Stripe API    │    │   OpenAI GPT-4  │    │   n8n Service   │
│   (Payments)    │    │   + Perplexity  │    │   (Automation)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**AI 服务架构：**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI       │    │   OpenAI GPT-4  │    │   Perplexity    │
│   AI Service    │◄──►│   (Primary)     │    │   (Secondary)   │
│                 │    │   - Text Gen    │    │   - Real-time   │
│                 │    │   - Optimization│    │   - Info        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## **2. 代码质量与开发规范**

### **2.1 编码规范与风格**

* **前端规范：** 遵循 Airbnb JavaScript Style Guide，使用 TypeScript 进行类型检查  
* **后端规范：** 遵循 PEP 8 (Python) 规范，使用 Black 进行代码格式化  
* **格式化工具：** 强制使用 Prettier (前端) 和 Black (Python)，配置规则文件位于项目根目录  
* **Linting 规则：** 必须通过 ESLint (前端) 和 Flake8 (Python) 的所有 Error 级别检查

### **2.2 代码审查 (Code Review) 规范**

* **Pull Request (PR) 要求：** PR 必须关联一个 GitHub Issue ID；必须通过所有 CI 检查；必须包含测试用例  
* **审查标准：** 关注业务逻辑正确性、错误处理、安全性、性能优化和代码可读性  
* **合并要求：** 必须至少获得 1 个团队成员的批准（Approve），P0 功能需要 2 个批准

### **2.3 文件组织规范**

```
project-root/
├── frontend/                 # Next.js 前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义 Hooks
│   │   ├── store/          # Zustand 状态管理
│   │   └── utils/          # 工具函数
├── backend/                 # 后端 API 服务
│   ├── src/
│   │   ├── routes/         # API 路由
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
├── docs/                   # 项目文档
└── tests/                  # 测试文件
```

## **3. 测试编写规范**

### **3.1 测试分层策略**

| 测试类型 | 覆盖目标 | 负责人 | 建议覆盖率 |
| :---- | :---- | :---- | :---- |
| **单元测试 (Unit Test)** | 单个函数/类，无外部依赖 | 开发工程师 | ≥ 80% |
| **集成测试 (Integration Test)** | 模块间接口、数据库或外部 API 交互 | 开发/测试工程师 | ≥ 50% |
| **端到端测试 (E2E Test)** | 模拟真实用户操作流程 | 测试工程师/QA | 关键业务路径 100% |

### **3.2 单元测试编写约定**

* **命名规范：** 测试文件命名为 `[原文件名].test.[后缀]`  
* **测试结构：** 使用 AAA 模式 (Arrange, Act, Assert)  
* **Mocking 策略：** 仅 Mock 外部 I/O 依赖（如数据库连接、API 调用）  
* **测试工具：** 前端使用 Jest + React Testing Library，后端使用 pytest (Python)

### **3.3 测试重点覆盖**

* **数据处理逻辑：** 简历数据验证、格式化、转换逻辑  
* **API 认证：** JWT Token 验证、权限检查  
* **AI Prompt 封装：** OpenAI GPT-4 和 Perplexity API 调用、错误处理、重试机制

## **4. 前端组件与 UX 设计规范**

### **4.1 通用组件库**

* **技术栈：** React + TypeScript + Tailwind CSS  
* **组件设计原则：** 单一职责、可复用、可测试  
* **组件文档：** 使用 Storybook 进行组件文档化  
* **无障碍性 (A11Y)：** 所有交互元素必须支持键盘导航和 ARIA 属性，符合 WCAG 2.1 Level AA 标准

### **4.2 状态管理规范**

* **Store 结构：** 
  ```typescript
  interface AppState {
    user: UserState;
    resume: ResumeState;
    subscription: SubscriptionState;
    ui: UIState;
  }
  ```
* **数据操作：** 所有状态修改必须通过 Zustand 的 Actions 进行  
* **持久化：** 用户认证状态和简历草稿需要持久化存储

### **4.3 响应式设计规范**

* **断点定义：** 
  - Mobile: < 768px
  - Tablet: 768px - 1024px  
  - Desktop: > 1024px
* **组件适配：** 所有组件必须支持移动端和桌面端  
* **性能优化：** 使用 React.memo、useMemo、useCallback 优化渲染性能

## **5. 安全与性能规范**

### **5.1 安全规范**

* **数据加密：** 敏感数据使用 AES-256 加密存储  
* **API 安全：** 所有 API 必须进行身份验证和授权检查  
* **输入验证：** 前后端都必须进行输入验证和清理  
* **HTTPS 强制：** 生产环境必须使用 HTTPS

### **5.2 性能规范**

* **API 响应时间：** 90% 的 API 请求必须在 300ms 内响应  
* **页面加载时间：** 首屏加载时间不超过 2 秒  
* **AI 优化响应：** AI 服务响应时间必须在 5 秒内  
* **缓存策略：** 静态资源使用 CDN 缓存，API 响应使用适当的缓存策略

**后续步骤：** 依据此规范，开发团队开始编写 API 契约和实现代码。