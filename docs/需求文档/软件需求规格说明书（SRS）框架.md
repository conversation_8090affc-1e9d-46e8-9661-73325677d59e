# **SynapseCV - 软件需求规格说明书 V1.0**

文件名称： SynapseCV - 软件需求规格说明书 V1.0  
创建人： 技术团队  
创建日期： 2024-12-19  
关联 PRD/版本： SynapseCV - 产品需求文档 V1.0 - MVP 启动版

## **1. 总体描述**

### **1.1 产品前景与目标用户**

**SynapseCV: One Master Profile. Infinite Targeted Resumes.**

SynapseCV 是一个基于 AI 的简历优化和动态版本管理平台，旨在为北美市场的 SDE/技术岗位求职者提供智能化的简历定制服务。

**目标用户群体：**
- **主要用户：** SDE（软件开发工程师）、数据科学家、技术项目经理
- **次要用户：** 技术招聘人员（未来扩展）
- **系统管理员：** 平台运营和维护人员

### **1.2 系统架构概览**

系统采用前后端分离的现代化架构：

* **前端层：** Next.js 应用 + PWA，提供用户界面和交互
* **后端层：** Python/FastAPI，处理业务逻辑和 API
* **数据层：** Supabase (PostgreSQL) 提供数据存储和 JSONB 支持
* **AI 服务：** OpenAI GPT-4 (主) + Perplexity (辅) 进行简历内容优化
* **支付服务：** Stripe 集成处理订阅支付
* **自动化服务：** n8n 处理邮件自动化流程

## **2. 具体功能需求（Functional Requirements）**

本节将 PRD 中的用户故事转化为具体的系统行为，按照模块划分：

### **2.1 模块 A：用户认证与账户管理**

| 需求 ID | PRD 引用 | 需求描述 | 输入/处理/输出 | 优先级 |
| :---- | :---- | :---- | :---- | :---- |
| **FR-A-001** | US-001 | 用户注册验证 | **输入：** 邮箱、密码；**处理：** 邮箱格式验证、密码强度检查；**输出：** 注册成功或错误信息。 | P0 |
| **FR-A-002** | US-001 | 用户登录验证 | **输入：** 邮箱、密码；**处理：** 密码哈希比对；**输出：** JWT Token 或错误信息。 | P0 |
| **FR-A-003** | N/A | 密码重置流程 | 系统需发送重置链接到用户邮箱，链接有效期 30 分钟。 | P1 |
| **FR-A-004** | US-005 | 订阅状态管理 | **输入：** 用户 ID；**处理：** 检查 Stripe 订阅状态；**输出：** 订阅状态和到期时间。 | P0 |
| **FR-A-005** | N/A | 永久试用机制 | **输入：** 用户注册信息；**处理：** 创建永久试用账户；**输出：** 试用账户状态（水印限制、无导出、数据不永久保存）。 | P0 |

### **2.2 模块 B：简历数据管理服务**

| 需求 ID | PRD 引用 | 需求描述 | 输入/处理/输出 | 优先级 |
| :---- | :---- | :---- | :---- | :---- |
| **FR-B-001** | US-001 | 结构化数据录入 | **输入：** 原始事实 (Raw Facts)；**处理：** 数据验证和结构化存储；**输出：** 存储成功确认。 | P0 |
| **FR-B-002** | US-002 | AI 子弹点优化 | **输入：** fact_id (UUID), target_focus (String)；**处理：** 调用 OpenAI GPT-4 和 Perplexity API 优化；**输出：** 优化后的 Bullet Points。 | P0 |
| **FR-B-005** | US-002 | 岗位类型优化策略 | **输入：** target_focus (SDE_General/BigTech_Scale/Data_ML/Leadership)；**处理：** 应用对应的 Prompt Engineering 策略；**输出：** 针对性的优化结果。 | P0 |
| **FR-B-003** | US-003 | 动态版本生成 | **输入：** 版本配置；**处理：** 基于 Profile 生成版本；**输出：** 新简历版本。 | P0 |
| **FR-B-004** | US-003 | 版本同步更新 | **输入：** 主数据源变更；**处理：** 自动更新所有相关版本；**输出：** 更新确认。 | P0 |

### **2.3 模块 C：简历渲染与导出服务**

| 需求 ID | PRD 引用 | 需求描述 | 输入/处理/输出 | 优先级 |
| :---- | :---- | :---- | :---- | :---- |
| **FR-C-001** | US-004 | PDF 简历生成 | **输入：** version_id；**处理：** 动态渲染 PDF；**输出：** PDF 文件流。 | P0 |
| **FR-C-002** | US-004 | ATS 模板应用 | **输入：** 简历数据；**处理：** 应用 ATS 友好模板；**输出：** 格式化简历。 | P0 |
| **FR-C-003** | N/A | 试用版水印限制 | **输入：** 用户类型（试用/付费）；**处理：** 试用版添加水印，禁用导出；**输出：** 带水印的预览或无导出权限。 | P0 |

### **2.4 模块 D：支付与订阅服务**

| 需求 ID | PRD 引用 | 需求描述 | 输入/处理/输出 | 优先级 |
| :---- | :---- | :---- | :---- | :---- |
| **FR-D-001** | US-005 | Stripe 支付集成 | **输入：** 支付信息；**处理：** 调用 Stripe API；**输出：** 支付结果。 | P0 |
| **FR-D-002** | US-005 | 订阅管理 | **输入：** 订阅操作；**处理：** 更新订阅状态；**输出：** 订阅确认。 | P0 |

## **3. 非功能性需求（Non-Functional Requirements - NFR）**

### **3.1 性能要求（Performance）**

* **响应时间：** 90% 的 API 请求必须在 300 毫秒内响应  
* **AI 优化响应：** OpenAI GPT-4 优化 Bullet Points 的响应时间必须在 5 秒内  
* **并发能力：** 系统必须支持至少 500 个并发用户会话  
* **加载时间：** 核心页面首次加载时间不超过 2 秒

### **3.2 安全性要求（Security）**

* **数据加密：** 简历内容必须被视为敏感数据，使用 AES-256 加密存储  
* **通信安全：** 所有客户端与服务器端的通信必须通过 HTTPS 传输  
* **攻击防护：** 防止 SQL 注入、XSS 等 OWASP Top 10 攻击  
* **认证安全：** 使用 JWT Token 进行用户认证，Token 有效期 24 小时

### **3.3 可靠性与可用性（Reliability & Availability）**

* **系统可用性目标（SLA）：** 99.9%  
* **备份与恢复：** 每天自动全量备份一次，数据恢复时间目标（RTO）小于 4 小时  
* **故障恢复：** 核心服务中断恢复时间目标（RTO）< 30 分钟

### **3.4 可维护性与可扩展性（Maintainability & Scalability）**

* **代码规范：** 代码必须遵循项目编码规范，关键模块需附带详细注释  
* **架构设计：** 系统采用分层架构，支持未来水平扩展  
* **测试覆盖：** 数据处理逻辑、API 认证、AI Prompt 封装覆盖率 ≥80%

## **4. 接口与数据需求**

### **4.1 用户接口（UI/UX）**

* **技术栈：** React + Zustand（轻量级状态管理）
* **响应式设计：** 支持桌面、平板、手机访问
* **无障碍性：** 遵循 WCAG 2.1 AA 标准
* **浏览器支持：** Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### **4.2 外部接口（External Interfaces）**

| 外部系统/API | 功能 | 协议/格式 | 限制 |
| :---- | :---- | :---- | :---- |
| **OpenAI GPT-4 API** | AI 简历优化（主） | HTTPS/JSON | 响应时间 < 5 秒，错误率 < 1% |
| **Perplexity API** | AI 信息补充（辅） | HTTPS/JSON | 响应时间 < 3 秒，错误率 < 2% |
| **Stripe API** | 支付处理 | HTTPS/JSON | 每秒调用上限 100 次 |
| **n8n Webhook** | 邮件自动化 | HTTPS/JSON | 异步处理，超时 30 秒 |
| **Supabase API** | 数据存储 | HTTPS/JSON | 连接池限制 100 个 |

### **4.3 数据模型（初步）**

**核心数据实体：**

* **ResumeProfile (用户主数据源)：** 
  - user_id (PK, UUID)
  - experience (JSON Array)
  - created_at, updated_at

* **ExperienceFact：** 
  - exp_id (PK, UUID)
  - raw_facts (原始事实)
  - optimized_bullets (AI 优化结果)
  - target_roles (目标岗位)

* **ResumeVersion：** 
  - version_id (PK, UUID)
  - user_id (FK)
  - included_experience_ids (引用 exp_id)
  - version_name, created_at

* **User：** 
  - user_id (PK, UUID)
  - email, password_hash
  - subscription_status, created_at

* **Subscription：** 
  - subscription_id (PK, UUID)
  - user_id (FK)
  - stripe_subscription_id
  - status, current_period_end

## **5. AI 优化策略详细说明**

### **5.1 岗位类型优化策略**

| 岗位类型 (target_focus) | 优化策略和 Prompt Engineering 焦点 | 数据库存储 (optimized_bullets) |
| :---- | :---- | :---- |
| **SDE 通用/初创公司** | 强调速度、迭代、全栈技能和解决问题的能力。Prompt 侧重使用 built, delivered, shipped 等强动词。 | optimized_bullets[SDE_General] |
| **FAANG/大厂** | 强调可扩展性、系统设计、跨团队协作和量化影响。Prompt 侧重使用 architected, scaled, reduced latency by X% 等量化和工程化术语。 | optimized_bullets[BigTech_Scale] |
| **数据科学/AI 岗位** | 强调数据量、算法、模型准确率和洞察力。Prompt 侧重使用 analyzed, modeled, improved accuracy by X% 等统计和 ML 术语。 | optimized_bullets[Data_ML] |
| **管理/领导力 (Eng Manager)** | 强调团队规模、指导、项目交付和跨部门影响。Prompt 侧重使用 led, mentored, managed。 | optimized_bullets[Leadership] |

### **5.2 AI 服务集成策略**

* **主要服务：** OpenAI GPT-4 负责核心文本生成和优化
* **辅助服务：** Perplexity 提供实时信息补充和行业趋势
* **降级策略：** GPT-4 不可用时，自动切换到 Perplexity 或使用预置模板

**后续步骤：** 本文档审批通过后，正式进入设计阶段（架构设计、数据库设计）。