# **SynapseCV - 原型与用户流程规划 V1.0**

文件名称： SynapseCV - 原型与用户流程规划 V1.0  
创建人： UX/UI 设计团队  
创建日期： 2024-12-19  
关联 PRD/SRS： SynapseCV - 产品需求文档 V1.0 - MVP 启动版

## **1. 原型资产概览**

| 类型 | 状态 | 制作工具 | 访问链接 |
| :---- | :---- | :---- | :---- |
| **低保真线框图** | 进行中 | Figma | [待创建链接] |
| **高保真交互稿** | 待开始 | Figma | [待创建链接] |
| **设计系统/组件库** | 待开始 | Figma | [待创建链接] |

## **2. 核心用户流程（User Flows）**

详细定义用户完成关键任务的每一步流程，用于指导原型制作和后续测试。

### **2.1 流程 A：首次用户注册与引导**

| 步骤 | 界面名称 (原型链接) | 用户操作 | 系统响应 (界面变化) | 关联需求 ID |
| :---- | :---- | :---- | :---- | :---- |
| **1** | 注册页 | 输入邮箱、密码，点击"注册"按钮 | 验证输入格式，显示加载动画 | FR-A-001 |
| **2** | 邮箱验证页 | 输入收到的 6 位验证码 | 验证通过，跳转到引导页 | FR-A-003 |
| **3** | 新手引导页 | 完成 3 步引导：了解功能、录入第一个项目、生成第一个版本 | 引导完成，将用户状态标记为"已完成引导" | N/A |
| **4** | 主控制台 | 显示用户的主数据源和简历版本列表 | 展示核心功能入口 | US-001 |

### **2.2 流程 B：简历数据录入与 AI 优化**

| 步骤 | 界面名称 (原型链接) | 用户操作 | 系统响应 (界面变化) | 关联需求 ID |
| :---- | :---- | :---- | :---- | :---- |
| **1** | 项目录入页 | 填写项目名称、公司、时间、原始描述 | 实时验证输入格式 | FR-B-001 |
| **2** | 技术栈选择 | 选择使用的技术栈和工具 | 显示技术栈标签 | FR-B-001 |
| **3** | AI 优化页 | 选择目标岗位，点击"AI 优化"按钮 | 显示优化进度，5秒内返回结果 | FR-B-002 |
| **4** | 优化结果页 | 查看优化后的 Bullet Points，可编辑或重新生成 | 展示优化前后对比 | FR-B-002 |

### **2.3 流程 C：动态版本生成与导出**

| 步骤 | 界面名称 (原型链接) | 用户操作 | 系统响应 (界面变化) | 关联需求 ID |
| :---- | :---- | :---- | :---- | :---- |
| **1** | 版本创建页 | 输入版本名称，选择要包含的项目经历 | 实时显示选中项目的预览 | FR-B-003 |
| **2** | 版本配置页 | 调整项目顺序，选择简历模板 | 显示简历预览 | FR-C-002 |
| **3** | 简历预览页 | 查看最终简历效果，确认无误 | 显示完整的简历预览 | FR-C-001 |
| **4** | 导出页面 | 点击"导出 PDF"按钮 | 生成并下载 PDF 文件 | FR-C-001 |

### **2.4 流程 D：订阅与支付**

| 步骤 | 界面名称 (原型链接) | 用户操作 | 系统响应 (界面变化) | 关联需求 ID |
| :---- | :---- | :---- | :---- | :---- |
| **1** | 订阅选择页 | 选择 $19/月订阅计划 | 显示订阅详情和功能对比 | FR-D-001 |
| **2** | 支付页面 | 输入支付信息，确认订阅 | 调用 Stripe API 处理支付 | FR-D-001 |
| **3** | 支付成功页 | 查看支付确认信息 | 激活订阅，发送确认邮件 | FR-D-002 |
| **4** | 账户管理页 | 查看订阅状态和管理选项 | 显示当前订阅信息和到期时间 | FR-D-002 |

## **3. 界面清单与状态管理（Screen Inventory & State）**

列出系统中所有独特的页面/组件，并考虑其不同的显示状态。

| 界面名称/组件名 | 关键状态（需设计） | 依赖数据/API | 备注 |
| :---- | :---- | :---- | :---- |
| **用户注册页** | 1. 正常状态；2. 输入验证错误；3. 提交中状态；4. 注册成功 | 用户注册 API | 需要实时验证邮箱格式 |
| **项目录入表单** | 1. 空白状态；2. 填写中；3. 验证错误；4. 提交成功 | 项目录入 API | 支持富文本编辑 |
| **AI 优化组件** | 1. 待优化；2. 优化中；3. 优化完成；4. 优化失败 | AI 优化 API | 需要进度指示器 |
| **简历预览组件** | 1. 加载中；2. 预览正常；3. 预览错误；4. 导出中 | PDF 生成 API | 支持实时预览更新 |
| **支付表单** | 1. 正常状态；2. 支付处理中；3. 支付成功；4. 支付失败 | Stripe API | 需要安全的支付处理 |
| **导航栏** | 1. 未登录状态；2. 已登录状态；3. 订阅用户；4. 试用用户 | 用户状态 API | 根据用户状态显示不同选项 |

## **4. 用户体验设计原则**

### **4.1 设计原则**

* **简洁性：** 界面简洁明了，减少用户认知负担
* **一致性：** 保持设计语言和交互模式的一致性
* **可访问性：** 支持键盘导航和屏幕阅读器，符合 WCAG 2.1 Level AA 标准
* **响应式：** 适配桌面、平板、手机等不同设备，PWA 支持

### **4.2 关键交互设计**

* **拖拽排序：** 项目经历和 Bullet Points 支持拖拽排序
* **实时预览：** 简历修改后实时显示预览效果
* **智能提示：** 在关键步骤提供智能提示和引导
* **错误处理：** 友好的错误提示和恢复建议

## **5. 反馈与迭代记录**

| 迭代日期 | 提出者 | 变更点/反馈内容 | 处理结果 |
| :---- | :---- | :---- | :---- |
| 2024-12-19 | 产品团队 | 需要增加试用期引导流程 | 已采纳，已更新流程 A |
| 2024-12-19 | 技术团队 | AI 优化过程需要更清晰的进度指示 | 已采纳，已更新流程 B |
| 2024-12-19 | 用户研究 | 支付流程需要更简洁的步骤 | 已采纳，已更新流程 D |

## **6. 用户测试计划**

### **6.1 测试策略**

**计划 (MVP):**

**冷启动：** 在 Reddit (r/cscareerquestions, r/EngineeringResumes) 社区招募 10-20 名 SDE 用户进行 Beta 测试

**测试周期：** 2 周

**任务：** 要求用户录入 2 份完整的经历，并生成 3 个不同版本的简历

### **6.2 关键指标 (KPI)**

**核心价值指标：** 用户完成"从原始事实到 AI 优化 Bullet Point"的满意度得分 (CSAT)

**效率指标：** 用户从零创建一份新简历并导出所需时间

**同步指标：** 用户修改 raw_facts 后，所有版本同步所需时间

**转化指标：** 试用用户到付费用户的转化率

## **7. 无障碍性要求**

### **7.1 合规标准**

**标准：** WCAG 2.1 Level AA

### **7.2 具体要求**

**键盘导航：** 所有表单元素和交互按钮必须能通过 Tab 键访问和操作

**颜色对比度：** 确保文本和背景的对比度符合 AA 级要求，尤其是在简历模板中

**ARIA 属性：** 使用 ARIA 标签，确保屏幕阅读器能正确描述复杂组件（如 Tag Input 组件）

**语义化 HTML：** 使用正确的 HTML 语义标签，提高可访问性

## **8. 原型制作计划**

### **8.1 制作时间线**

* **Week 1-2：** 完成低保真线框图
* **Week 3-4：** 完成高保真交互稿
* **Week 5：** 用户测试和迭代
* **Week 6：** 最终设计资源交付

### **8.2 设计资源交付**

* **设计稿：** Figma 源文件和导出图片
* **组件库：** 可复用的 UI 组件
* **设计规范：** 颜色、字体、间距等设计规范
* **交互说明：** 详细的交互行为说明文档
* **无障碍性测试报告：** 可访问性测试结果和改进建议

**后续步骤：** 原型经过用户测试和利益相关者确认后，UI/UX 设计师将最终的设计资源交付给开发团队。