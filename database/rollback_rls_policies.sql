-- =====================================================
-- 回滚 user_profiles 表的 RLS 策略
-- 
-- 如果新的策略有问题，可以使用此脚本回滚到原始状态
-- 
-- 使用方法：
-- 1. 在 Supabase Dashboard 中打开 SQL Editor
-- 2. 复制粘贴此脚本
-- 3. 点击运行执行
-- =====================================================

-- 1. 删除所有新创建的策略
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can delete own profile" ON public.user_profiles;

-- 2. 恢复原始的简单策略
CREATE POLICY "Users can manage own profile" ON public.user_profiles
    FOR ALL USING (auth.uid() = user_id);

-- 3. 验证回滚结果
SELECT 
    'Rollback completed:' as info,
    COALESCE(policyname, 'No policies found') as policy_name,
    COALESCE(cmd::text, 'N/A') as command,
    COALESCE(permissive::text, 'N/A') as permissive
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'user_profiles';

-- 完成提示
SELECT 'RLS policies have been rolled back to original state!' as result,
       'Note: This may cause the same registration issue to occur again.' as warning;

