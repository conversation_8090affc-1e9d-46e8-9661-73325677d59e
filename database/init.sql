/**
 * SynapseCV 数据库初始化脚本
 * 
 * 基于数据库设计文档创建的Supabase/PostgreSQL初始化SQL
 * 包含完整的表结构、索引、RLS策略和必要的函数
 * 
 * 创建日期: 2024-12-19
 * 版本: 1.0
 * 
 * TODO: 定期检查并更新索引策略以优化查询性能
 * TODO: 根据业务增长调整RLS策略
 * TODO: 考虑添加数据归档策略
 */

-- =====================================================
-- 1. 启用必要的扩展
-- =====================================================

-- 启用UUID生成扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 启用pgcrypto扩展用于加密功能
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- 2. 创建核心数据表
-- =====================================================

-- 用户基础信息表
-- 存储用户的个人基础信息，与auth.users表关联
CREATE TABLE IF NOT EXISTS public.user_profiles (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    title TEXT,
    contact_info JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 主经历事实表 (SSOT - Single Source of Truth)
-- 存储用户录入的原子化原始事实，是系统的核心数据源
CREATE TABLE IF NOT EXISTS public.master_experiences (
    exp_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_name TEXT NOT NULL,
    role_title TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    raw_facts JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI优化内容表
-- 存储AI生成的优化内容，与原始事实分离以便版本迭代
CREATE TABLE IF NOT EXISTS public.optimized_content (
    opt_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    exp_id UUID NOT NULL REFERENCES public.master_experiences(exp_id) ON DELETE CASCADE,
    target_focus TEXT NOT NULL,
    optimized_bullets JSONB NOT NULL DEFAULT '[]'::jsonb,
    ai_model TEXT NOT NULL DEFAULT 'gpt-4',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 简历版本定义表
-- 存储简历版本配置，支持动态渲染
CREATE TABLE IF NOT EXISTS public.resume_versions (
    version_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    version_name TEXT NOT NULL,
    template_id TEXT NOT NULL DEFAULT 'default',
    version_config JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- n8n自动化日志表
-- 记录n8n触发的所有自动化事件，支持运营分析
CREATE TABLE IF NOT EXISTS public.n8n_automation_log (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL DEFAULT '{}'::jsonb,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 3. 创建性能优化索引
-- =====================================================

-- 用户相关索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_updated_at ON public.user_profiles(updated_at);

-- 经历相关索引
CREATE INDEX IF NOT EXISTS idx_master_experiences_user_id ON public.master_experiences(user_id);
CREATE INDEX IF NOT EXISTS idx_master_experiences_company_name ON public.master_experiences(company_name);
CREATE INDEX IF NOT EXISTS idx_master_experiences_start_date ON public.master_experiences(start_date);
CREATE INDEX IF NOT EXISTS idx_master_experiences_created_at ON public.master_experiences(created_at);

-- JSONB字段GIN索引 - 高性能关键词查询
CREATE INDEX IF NOT EXISTS idx_master_experiences_raw_facts_gin ON public.master_experiences USING GIN (raw_facts);
CREATE INDEX IF NOT EXISTS idx_user_profiles_contact_info_gin ON public.user_profiles USING GIN (contact_info);

-- 优化内容相关索引
CREATE INDEX IF NOT EXISTS idx_optimized_content_exp_id ON public.optimized_content(exp_id);
CREATE INDEX IF NOT EXISTS idx_optimized_content_target_focus ON public.optimized_content(target_focus);
CREATE INDEX IF NOT EXISTS idx_optimized_content_ai_model ON public.optimized_content(ai_model);
CREATE INDEX IF NOT EXISTS idx_optimized_content_created_at ON public.optimized_content(created_at);

-- JSONB字段GIN索引
CREATE INDEX IF NOT EXISTS idx_optimized_content_bullets_gin ON public.optimized_content USING GIN (optimized_bullets);

-- 简历版本相关索引
CREATE INDEX IF NOT EXISTS idx_resume_versions_user_id ON public.resume_versions(user_id);
CREATE INDEX IF NOT EXISTS idx_resume_versions_template_id ON public.resume_versions(template_id);
CREATE INDEX IF NOT EXISTS idx_resume_versions_created_at ON public.resume_versions(created_at);

-- JSONB字段GIN索引
CREATE INDEX IF NOT EXISTS idx_resume_versions_config_gin ON public.resume_versions USING GIN (version_config);

-- 自动化日志相关索引
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_user_id ON public.n8n_automation_log(user_id);
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_event_type ON public.n8n_automation_log(event_type);
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_status ON public.n8n_automation_log(status);
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_created_at ON public.n8n_automation_log(created_at);

-- JSONB字段GIN索引
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_payload_gin ON public.n8n_automation_log USING GIN (payload);

-- =====================================================
-- 4. 创建更新时间触发器函数
-- =====================================================

-- 通用的updated_at字段更新函数
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建updated_at触发器
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_master_experiences_updated_at 
    BEFORE UPDATE ON public.master_experiences 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_optimized_content_updated_at 
    BEFORE UPDATE ON public.optimized_content 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_resume_versions_updated_at 
    BEFORE UPDATE ON public.resume_versions 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- 5. 启用行级安全策略 (RLS)
-- =====================================================

-- 启用所有表的RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.optimized_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resume_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.n8n_automation_log ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 6. 创建RLS安全策略
-- =====================================================

-- 用户基础信息表策略
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own profile" ON public.user_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- 主经历事实表策略
CREATE POLICY "Users can view own experiences" ON public.master_experiences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own experiences" ON public.master_experiences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own experiences" ON public.master_experiences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own experiences" ON public.master_experiences
    FOR DELETE USING (auth.uid() = user_id);

-- AI优化内容表策略
CREATE POLICY "Users can view own optimized content" ON public.optimized_content
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.master_experiences 
            WHERE exp_id = optimized_content.exp_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own optimized content" ON public.optimized_content
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.master_experiences 
            WHERE exp_id = optimized_content.exp_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update own optimized content" ON public.optimized_content
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.master_experiences 
            WHERE exp_id = optimized_content.exp_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete own optimized content" ON public.optimized_content
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.master_experiences 
            WHERE exp_id = optimized_content.exp_id 
            AND user_id = auth.uid()
        )
    );

-- 简历版本表策略
CREATE POLICY "Users can view own resume versions" ON public.resume_versions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own resume versions" ON public.resume_versions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own resume versions" ON public.resume_versions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own resume versions" ON public.resume_versions
    FOR DELETE USING (auth.uid() = user_id);

-- n8n自动化日志表策略
CREATE POLICY "Users can view own automation logs" ON public.n8n_automation_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert automation logs" ON public.n8n_automation_log
    FOR INSERT WITH CHECK (true); -- 允许系统插入日志

CREATE POLICY "Users can update own automation logs" ON public.n8n_automation_log
    FOR UPDATE USING (auth.uid() = user_id);

-- =====================================================
-- 7. 创建有用的数据库函数
-- =====================================================

-- 获取用户完整简历数据的函数
CREATE OR REPLACE FUNCTION public.get_user_resume_data(p_user_id UUID)
RETURNS TABLE (
    exp_id UUID,
    company_name TEXT,
    role_title TEXT,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN,
    raw_facts JSONB,
    optimized_content JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        me.exp_id,
        me.company_name,
        me.role_title,
        me.start_date,
        me.end_date,
        me.is_current,
        me.raw_facts,
        COALESCE(
            json_agg(
                json_build_object(
                    'opt_id', oc.opt_id,
                    'target_focus', oc.target_focus,
                    'optimized_bullets', oc.optimized_bullets,
                    'ai_model', oc.ai_model,
                    'created_at', oc.created_at
                )
            ) FILTER (WHERE oc.opt_id IS NOT NULL),
            '[]'::json
        ) as optimized_content
    FROM public.master_experiences me
    LEFT JOIN public.optimized_content oc ON me.exp_id = oc.exp_id
    WHERE me.user_id = p_user_id
    GROUP BY me.exp_id, me.company_name, me.role_title, me.start_date, me.end_date, me.is_current, me.raw_facts
    ORDER BY me.start_date DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 搜索用户经历的函数
CREATE OR REPLACE FUNCTION public.search_user_experiences(
    p_user_id UUID,
    p_search_term TEXT DEFAULT NULL,
    p_company_filter TEXT DEFAULT NULL
)
RETURNS TABLE (
    exp_id UUID,
    company_name TEXT,
    role_title TEXT,
    start_date DATE,
    end_date DATE,
    is_current BOOLEAN,
    raw_facts JSONB,
    relevance_score REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        me.exp_id,
        me.company_name,
        me.role_title,
        me.start_date,
        me.end_date,
        me.is_current,
        me.raw_facts,
        CASE 
            WHEN p_search_term IS NOT NULL THEN
                ts_rank(
                    to_tsvector('english', me.company_name || ' ' || me.role_title || ' ' || me.raw_facts::text),
                    plainto_tsquery('english', p_search_term)
                )
            ELSE 1.0
        END as relevance_score
    FROM public.master_experiences me
    WHERE me.user_id = p_user_id
    AND (p_company_filter IS NULL OR me.company_name ILIKE '%' || p_company_filter || '%')
    AND (p_search_term IS NULL OR 
         to_tsvector('english', me.company_name || ' ' || me.role_title || ' ' || me.raw_facts::text) 
         @@ plainto_tsquery('english', p_search_term))
    ORDER BY relevance_score DESC, me.start_date DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 8. 创建数据验证约束
-- =====================================================

-- 确保经历的开始日期不晚于结束日期
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_dates_valid 
CHECK (end_date IS NULL OR start_date <= end_date);

-- 确保当前工作经历没有结束日期
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_current_job_no_end_date 
CHECK (is_current = FALSE OR end_date IS NULL);

-- 确保版本名称不为空
ALTER TABLE public.resume_versions 
ADD CONSTRAINT check_version_name_not_empty 
CHECK (LENGTH(TRIM(version_name)) > 0);

-- 确保公司名称不为空
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_company_name_not_empty 
CHECK (LENGTH(TRIM(company_name)) > 0);

-- 确保角色标题不为空
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_role_title_not_empty 
CHECK (LENGTH(TRIM(role_title)) > 0);

-- =====================================================
-- 9. 创建初始数据（可选）
-- =====================================================

-- 创建默认模板配置
INSERT INTO public.resume_versions (user_id, version_name, template_id, version_config)
SELECT 
    auth.uid(),
    'Default Template',
    'default',
    '{"sections": ["experience", "education", "skills"], "format": "chronological"}'::jsonb
WHERE auth.uid() IS NOT NULL
ON CONFLICT DO NOTHING;

-- =====================================================
-- 10. 创建性能监控视图
-- =====================================================

-- 用户数据统计视图
CREATE OR REPLACE VIEW public.user_stats AS
SELECT 
    u.id as user_id,
    u.email,
    up.full_name,
    COUNT(DISTINCT me.exp_id) as total_experiences,
    COUNT(DISTINCT oc.opt_id) as total_optimizations,
    COUNT(DISTINCT rv.version_id) as total_versions,
    MAX(me.created_at) as last_experience_added,
    MAX(oc.created_at) as last_optimization
FROM auth.users u
LEFT JOIN public.user_profiles up ON u.id = up.user_id
LEFT JOIN public.master_experiences me ON u.id = me.user_id
LEFT JOIN public.optimized_content oc ON me.exp_id = oc.exp_id
LEFT JOIN public.resume_versions rv ON u.id = rv.user_id
GROUP BY u.id, u.email, up.full_name;

-- 系统使用统计视图
CREATE OR REPLACE VIEW public.system_stats AS
SELECT 
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT me.exp_id) as total_experiences,
    COUNT(DISTINCT oc.opt_id) as total_optimizations,
    COUNT(DISTINCT rv.version_id) as total_versions,
    AVG(CASE WHEN me.exp_id IS NOT NULL THEN 1.0 ELSE 0.0 END) as avg_experiences_per_user,
    AVG(CASE WHEN oc.opt_id IS NOT NULL THEN 1.0 ELSE 0.0 END) as avg_optimizations_per_user
FROM auth.users u
LEFT JOIN public.master_experiences me ON u.id = me.user_id
LEFT JOIN public.optimized_content oc ON me.exp_id = oc.exp_id
LEFT JOIN public.resume_versions rv ON u.id = rv.user_id;

-- =====================================================
-- 完成初始化
-- =====================================================

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE 'SynapseCV数据库初始化完成！';
    RAISE NOTICE '已创建的表: user_profiles, master_experiences, optimized_content, resume_versions, n8n_automation_log';
    RAISE NOTICE '已启用行级安全策略(RLS)';
    RAISE NOTICE '已创建性能优化索引';
    RAISE NOTICE '已创建数据库函数和视图';
END $$;
