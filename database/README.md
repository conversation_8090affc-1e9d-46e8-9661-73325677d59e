# SynapseCV 数据库初始化指南

## 概述

本目录包含SynapseCV项目的数据库初始化SQL脚本，专为Supabase/PostgreSQL设计。根据项目文档中的数据库设计契约创建，确保数据一致性、安全性和高性能。

## 文件说明

### 1. `init.sql` - 完整初始化脚本
- **用途**: 完整的数据库初始化，包含所有功能
- **包含内容**:
  - 完整的表结构定义
  - 性能优化索引（包括JSONB GIN索引）
  - 行级安全策略(RLS)
  - 数据库函数和触发器
  - 数据验证约束
  - 统计视图
- **适用场景**: 生产环境部署、完整功能开发

### 2. `supabase_migration.sql` - Supabase专用迁移脚本
- **用途**: 专为Supabase SQL编辑器设计的简化版本
- **包含内容**:
  - 核心表结构
  - 基础索引和JSONB GIN索引
  - RLS策略
  - 数据验证约束
- **适用场景**: Supabase Dashboard快速部署、开发测试

## 数据库表结构

### 核心表设计

| 表名 | 用途 | 关键字段 | 关系 |
|------|------|----------|------|
| `user_profiles` | 用户基础信息 | user_id, full_name, contact_info | 1:1 with auth.users |
| `master_experiences` | 主经历事实表(SSOT) | exp_id, user_id, raw_facts | 1:N with auth.users |
| `optimized_content` | AI优化内容 | opt_id, exp_id, optimized_bullets | 1:N with master_experiences |
| `resume_versions` | 简历版本定义 | version_id, user_id, version_config | 1:N with auth.users |
| `n8n_automation_log` | 自动化日志 | log_id, user_id, payload | 1:N with auth.users |

### 关键设计特点

1. **单一事实来源(SSOT)**: `master_experiences`表作为所有经历数据的唯一来源
2. **JSONB优化**: 使用JSONB存储半结构化数据，配合GIN索引实现高性能查询
3. **行级安全(RLS)**: 确保用户只能访问自己的数据
4. **版本管理**: 通过引用机制实现高效的简历版本管理

## 部署指南

### 方法一: 使用Supabase Dashboard (推荐)

1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择你的项目
3. 进入 **SQL Editor**
4. 复制 `supabase_migration.sql` 的内容
5. 粘贴到SQL编辑器中
6. 点击 **Run** 执行

### 方法二: 使用完整初始化脚本

1. 在Supabase Dashboard的SQL Editor中
2. 复制 `init.sql` 的内容
3. 执行完整的初始化脚本

### 方法三: 使用Supabase CLI

```bash
# 安装Supabase CLI
npm install -g supabase

# 登录
supabase login

# 链接项目
supabase link --project-ref your-project-ref

# 应用迁移
supabase db push
```

## 验证部署

执行以下查询验证表是否创建成功：

```sql
-- 检查表是否存在
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'user_profiles', 
    'master_experiences', 
    'optimized_content', 
    'resume_versions', 
    'n8n_automation_log'
);

-- 检查RLS是否启用
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN (
    'user_profiles', 
    'master_experiences', 
    'optimized_content', 
    'resume_versions', 
    'n8n_automation_log'
);

-- 检查索引是否创建
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname LIKE 'idx_%';
```

## 性能优化

### JSONB查询优化

利用GIN索引进行高性能JSONB查询：

```sql
-- 搜索包含特定关键词的经历
SELECT * FROM master_experiences 
WHERE raw_facts @> '{"technologies": ["React"]}'::jsonb;

-- 全文搜索
SELECT * FROM master_experiences 
WHERE to_tsvector('english', raw_facts::text) @@ plainto_tsquery('english', 'React Node.js');
```

### 索引使用建议

1. **GIN索引**: 用于JSONB字段的包含查询和全文搜索
2. **B-tree索引**: 用于等值查询和范围查询
3. **复合索引**: 根据查询模式创建多列索引

## 安全考虑

### 行级安全策略(RLS)

所有表都启用了RLS，确保：
- 用户只能访问自己的数据
- 系统可以插入自动化日志
- 数据隔离和隐私保护

### 数据验证

- 日期逻辑验证（开始日期不能晚于结束日期）
- 非空字段验证
- 当前工作状态验证

## 维护和监控

### 定期维护任务

1. **索引维护**: 定期分析查询性能，调整索引策略
2. **数据清理**: 清理过期的自动化日志
3. **统计更新**: 更新表统计信息以优化查询计划

### 监控查询

```sql
-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看索引使用情况
SELECT 
    indexrelname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_tup_read DESC;
```

## 故障排除

### 常见问题

1. **权限错误**: 确保使用正确的数据库用户权限
2. **外键约束**: 检查auth.users表是否存在
3. **RLS策略**: 验证RLS策略是否正确配置

### 调试步骤

1. 检查SQL语法错误
2. 验证表依赖关系
3. 测试RLS策略
4. 检查索引创建状态

## 版本历史

- **v1.0** (2024-12-19): 初始版本，基于数据库设计文档创建
  - 完整的表结构定义
  - RLS安全策略
  - 性能优化索引
  - 数据验证约束

## 相关文档

- [数据库设计与契约文档](../docs/部署与开发文档/数据库设计与契约.md)
- [API契约文档](../docs/部署与开发文档/API%20契约与后端实现文档.md)
- [架构规范文档](../docs/部署与开发文档/架构与规范文档.md)

## 支持

如有问题，请参考：
1. Supabase官方文档
2. PostgreSQL官方文档
3. 项目内部文档
