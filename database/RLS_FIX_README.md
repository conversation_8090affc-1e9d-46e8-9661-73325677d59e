# RLS 策略修复指南

## 🚨 问题描述

用户注册时出现错误：
```
new row violates row-level security policy for table "user_profiles"
```

## 🔍 问题原因

现有的RLS策略不允许用户创建自己的用户资料记录，导致注册流程失败。

## 🛠️ 解决方案

### 方法1：使用Supabase Dashboard（推荐）

1. **打开Supabase Dashboard**
   - 进入你的项目
   - 点击左侧菜单的 "SQL Editor"

2. **执行修复脚本**
   - 复制 `fix_rls_policies.sql` 的内容
   - 粘贴到SQL Editor中
   - 点击 "Run" 执行

3. **验证修复结果**
   - 脚本会显示当前策略状态
   - 确认新策略已创建成功

### 方法2：使用Supabase CLI（高级用户）

```bash
# 1. 链接到远程项目
supabase link --project-ref YOUR_PROJECT_REF

# 2. 应用迁移
supabase db push

# 3. 验证迁移
supabase db diff
```

## 📋 修复内容

### 删除的策略
- `Users can manage own profile` (原始策略)

### 创建的新策略
- `Users can view own profile` - 允许用户查看自己的资料
- `Users can insert own profile` - 允许用户创建自己的资料
- `Users can update own profile` - 允许用户更新自己的资料
- `Users can delete own profile` - 允许用户删除自己的资料

## 🔒 安全性

新策略保持了相同的安全级别：
- ✅ 用户只能操作自己的数据 (`auth.uid() = user_id`)
- ✅ 需要有效的认证用户 (`auth.uid() IS NOT NULL`)
- ✅ 数据完整性检查 (`user_id IS NOT NULL`)
- ✅ 分离的策略提供更细粒度的控制

## 🧪 测试验证

修复后，请测试以下功能：

1. **新用户注册**
   - 尝试注册新用户
   - 确认不再出现RLS错误
   - 验证用户资料自动创建

2. **现有用户登录**
   - 尝试登录现有用户
   - 确认用户资料正常加载

3. **用户资料管理**
   - 更新用户信息
   - 确认操作正常

## 🔄 回滚方案

如果修复后出现问题，可以使用回滚脚本：

1. 在Supabase Dashboard的SQL Editor中
2. 复制 `rollback_rls_policies.sql` 的内容
3. 执行回滚脚本

## 📊 监控建议

修复后，建议监控以下指标：

- 用户注册成功率
- 认证错误率
- 数据库查询性能

## 🆘 故障排除

### 如果修复后仍有问题

1. **检查策略状态**
   ```sql
   SELECT policyname, cmd FROM pg_policies 
   WHERE schemaname = 'public' AND tablename = 'user_profiles';
   ```

2. **检查RLS状态**
   ```sql
   SELECT tablename, rowsecurity FROM pg_tables 
   WHERE schemaname = 'public' AND tablename = 'user_profiles';
   ```

3. **检查用户认证**
   ```sql
   SELECT auth.uid() as current_user_id;
   ```

### 常见问题

**Q: 修复后用户仍然无法注册**
A: 检查是否有其他RLS策略冲突，或尝试回滚后重新应用

**Q: 现有用户无法登录**
A: 检查用户资料是否存在，可能需要手动创建

**Q: 策略创建失败**
A: 确保有足够的权限，或联系Supabase支持

## 📞 支持

如果遇到问题：
1. 查看Supabase Dashboard的日志
2. 检查浏览器控制台错误
3. 参考Supabase官方文档
4. 联系技术支持

---

**注意**: 此修复是向后兼容的，不会影响现有功能。

