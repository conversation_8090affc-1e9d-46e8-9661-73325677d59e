-- =====================================================
-- 修复 user_profiles 表的 RLS 策略
-- 
-- 问题：现有策略不允许用户创建自己的用户资料
-- 解决：创建分离的策略，允许用户进行所有必要的操作
-- 
-- 安全性：
-- - 用户只能操作自己的数据 (auth.uid() = user_id)
-- - 分离的策略提供更细粒度的控制
-- - 保持最小权限原则
-- 
-- 使用方法：
-- 1. 在 Supabase Dashboard 中打开 SQL Editor
-- 2. 复制粘贴此脚本
-- 3. 点击运行执行
-- =====================================================

-- 1. 检查当前策略状态
SELECT 
    'Current policies:' as info,
    COALESCE(policyname, 'No policies found') as policy_name,
    COALESCE(cmd::text, 'N/A') as command,
    COALESCE(permissive::text, 'N/A') as permissive
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'user_profiles'
UNION ALL
SELECT 
    'Table status:' as info,
    'user_profiles' as policy_name,
    CASE WHEN EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_profiles') 
         THEN 'Table exists' 
         ELSE 'Table does not exist' 
    END as command,
    CASE WHEN EXISTS (SELECT 1 FROM pg_tables WHERE schemaname = 'public' AND tablename = 'user_profiles' AND rowsecurity = true)
         THEN 'RLS enabled' 
         ELSE 'RLS disabled' 
    END as permissive;

-- 2. 安全地删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can manage own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.user_profiles;
DROP POLICY IF EXISTS "Users can delete own profile" ON public.user_profiles;

-- 3. 创建新的分离策略（更安全、更清晰）

-- 允许用户查看自己的资料
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT 
    USING (
        auth.uid() = user_id 
        AND auth.uid() IS NOT NULL
    );

-- 允许用户插入自己的资料（注册时使用）
CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT 
    WITH CHECK (
        auth.uid() = user_id 
        AND auth.uid() IS NOT NULL
        AND user_id IS NOT NULL
    );

-- 允许用户更新自己的资料
CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE 
    USING (
        auth.uid() = user_id 
        AND auth.uid() IS NOT NULL
    )
    WITH CHECK (
        auth.uid() = user_id 
        AND auth.uid() IS NOT NULL
        AND user_id IS NOT NULL
    );

-- 允许用户删除自己的资料
CREATE POLICY "Users can delete own profile" ON public.user_profiles
    FOR DELETE 
    USING (
        auth.uid() = user_id 
        AND auth.uid() IS NOT NULL
    );

-- 4. 确保 RLS 已启用
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 5. 验证策略创建成功
SELECT 
    'New policies created:' as info,
    policyname as policy_name,
    cmd::text as command,
    permissive::text as permissive
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'user_profiles'
ORDER BY policyname;

-- 6. 验证 RLS 状态
SELECT 
    'RLS status:' as info,
    tablename as policy_name,
    CASE WHEN rowsecurity THEN 'Enabled' ELSE 'Disabled' END as command,
    'Security' as permissive
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename = 'user_profiles';

-- 7. 测试策略（可选 - 需要已登录用户）
-- 注意：这个测试需要以已登录用户身份执行
-- SELECT 
--     'Test auth.uid():' as info,
--     COALESCE(auth.uid()::text, 'No user logged in') as policy_name,
--     'Current user ID' as command,
--     'Test' as permissive;

-- 完成提示
SELECT 'RLS policies for user_profiles table have been successfully fixed!' as result,
       'You can now register new users without RLS policy errors.' as message;

