/**
 * SynapseCV Supabase 数据库迁移脚本
 * 
 * 专为Supabase SQL编辑器设计的简化版本
 * 包含核心表结构、索引和RLS策略
 * 
 * 使用方法：
 * 1. 在Supabase Dashboard中打开SQL Editor
 * 2. 复制粘贴此脚本
 * 3. 点击运行执行
 * 
 * 创建日期: 2024-12-19
 * 版本: 1.0
 */

-- =====================================================
-- 1. 创建核心数据表
-- =====================================================

-- 用户基础信息表
CREATE TABLE IF NOT EXISTS public.user_profiles (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT,
    title TEXT,
    contact_info JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 主经历事实表 (SSOT)
CREATE TABLE IF NOT EXISTS public.master_experiences (
    exp_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_name TEXT NOT NULL,
    role_title TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT FALSE,
    raw_facts JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AI优化内容表
CREATE TABLE IF NOT EXISTS public.optimized_content (
    opt_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exp_id UUID NOT NULL REFERENCES public.master_experiences(exp_id) ON DELETE CASCADE,
    target_focus TEXT NOT NULL,
    optimized_bullets JSONB NOT NULL DEFAULT '[]'::jsonb,
    ai_model TEXT NOT NULL DEFAULT 'gpt-4',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 简历版本定义表
CREATE TABLE IF NOT EXISTS public.resume_versions (
    version_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    version_name TEXT NOT NULL,
    template_id TEXT NOT NULL DEFAULT 'default',
    version_config JSONB NOT NULL DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- n8n自动化日志表
CREATE TABLE IF NOT EXISTS public.n8n_automation_log (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL,
    payload JSONB NOT NULL DEFAULT '{}'::jsonb,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- 2. 创建性能优化索引
-- =====================================================

-- 基础索引
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_master_experiences_user_id ON public.master_experiences(user_id);
CREATE INDEX IF NOT EXISTS idx_optimized_content_exp_id ON public.optimized_content(exp_id);
CREATE INDEX IF NOT EXISTS idx_resume_versions_user_id ON public.resume_versions(user_id);
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_user_id ON public.n8n_automation_log(user_id);

-- JSONB字段GIN索引 - 高性能查询
CREATE INDEX IF NOT EXISTS idx_master_experiences_raw_facts_gin ON public.master_experiences USING GIN (raw_facts);
CREATE INDEX IF NOT EXISTS idx_optimized_content_bullets_gin ON public.optimized_content USING GIN (optimized_bullets);
CREATE INDEX IF NOT EXISTS idx_resume_versions_config_gin ON public.resume_versions USING GIN (version_config);
CREATE INDEX IF NOT EXISTS idx_n8n_automation_log_payload_gin ON public.n8n_automation_log USING GIN (payload);

-- =====================================================
-- 3. 创建更新时间触发器
-- =====================================================

-- 通用更新函数
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_user_profiles_updated_at 
    BEFORE UPDATE ON public.user_profiles 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_master_experiences_updated_at 
    BEFORE UPDATE ON public.master_experiences 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_optimized_content_updated_at 
    BEFORE UPDATE ON public.optimized_content 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_resume_versions_updated_at 
    BEFORE UPDATE ON public.resume_versions 
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- =====================================================
-- 4. 启用行级安全策略 (RLS)
-- =====================================================

-- 启用RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_experiences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.optimized_content ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resume_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.n8n_automation_log ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 5. 创建RLS安全策略
-- =====================================================

-- 用户基础信息表策略
CREATE POLICY "Users can manage own profile" ON public.user_profiles
    FOR ALL USING (auth.uid() = user_id);

-- 主经历事实表策略
CREATE POLICY "Users can manage own experiences" ON public.master_experiences
    FOR ALL USING (auth.uid() = user_id);

-- AI优化内容表策略
CREATE POLICY "Users can manage own optimized content" ON public.optimized_content
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.master_experiences 
            WHERE exp_id = optimized_content.exp_id 
            AND user_id = auth.uid()
        )
    );

-- 简历版本表策略
CREATE POLICY "Users can manage own resume versions" ON public.resume_versions
    FOR ALL USING (auth.uid() = user_id);

-- n8n自动化日志表策略
CREATE POLICY "Users can view own automation logs" ON public.n8n_automation_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert automation logs" ON public.n8n_automation_log
    FOR INSERT WITH CHECK (true);

-- =====================================================
-- 6. 创建数据验证约束
-- =====================================================

-- 日期验证
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_dates_valid 
CHECK (end_date IS NULL OR start_date <= end_date);

-- 当前工作验证
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_current_job_no_end_date 
CHECK (is_current = FALSE OR end_date IS NULL);

-- 非空验证
ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_company_name_not_empty 
CHECK (LENGTH(TRIM(company_name)) > 0);

ALTER TABLE public.master_experiences 
ADD CONSTRAINT check_role_title_not_empty 
CHECK (LENGTH(TRIM(role_title)) > 0);

ALTER TABLE public.resume_versions 
ADD CONSTRAINT check_version_name_not_empty 
CHECK (LENGTH(TRIM(version_name)) > 0);

-- =====================================================
-- 完成提示
-- =====================================================

-- 输出完成信息
SELECT 'SynapseCV数据库迁移完成！' as status;
